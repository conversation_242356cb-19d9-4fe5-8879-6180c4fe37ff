import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SUPABASE_CONFIG } from '../constants/config';

// Supabase client oluştur - React Native için AsyncStorage ile
// Realtime özelliklerini devre dışı bırakarak WebSocket sorunlarını önle
export const supabase = createClient(
  SUPABASE_CONFIG.url,
  SUPABASE_CONFIG.anonKey,
  {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: false, // Otomatik session restore'u engelle
      detectSessionInUrl: false, // Mobile app için gerekli değil
      // Refresh token hatalarını daha iyi handle et
      flowType: 'pkce',
    },
    // Realtime'ı tamamen devre dışı bırak
    realtime: {
      params: {
        eventsPerSecond: 0, // Realtime events'i devre dışı bırak
      },
    },
    global: {
      headers: {
        'X-Client-Info': 'supabase-js-react-native',
      },
    },
  }
);

// Admin client for server-side operations (using service key)
export const supabaseAdmin = createClient(
  SUPABASE_CONFIG.url,
  SUPABASE_CONFIG.serviceKey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Global error handler for auth errors
supabase.auth.onAuthStateChange((event, session) => {
  if (event === 'TOKEN_REFRESHED' && !session) {
    console.log('Supabase: Token refresh failed, session will be cleared');
  }
});



// Database Types (Based on DATABASE-SYSTEM.md)
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          role: 'ADMIN' | 'DRIVER' | 'FAMILY';
          full_name: string;
          email: string;
          phone: string | null;
          status: 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE';
          created_at: string;
        };
        Insert: {
          id?: string;
          role: 'ADMIN' | 'DRIVER' | 'FAMILY';
          full_name: string;
          email: string;
          phone?: string | null;
          status?: 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE';
          created_at?: string;
        };
        Update: {
          id?: string;
          role?: 'ADMIN' | 'DRIVER' | 'FAMILY';
          full_name?: string;
          email?: string;
          phone?: string | null;
          status?: 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE';
          created_at?: string;
        };
      };
      deceased: {
        Row: {
          id: string;
          ditib_member_id: string | null;
          full_name: string;
          nationality: string | null;
          gender: string | null;
          date_of_death: string;
          place_of_death: string | null;
          place_of_burial: string | null;
          family_name: string | null;
          family_email: string | null;
          family_phone: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          ditib_member_id?: string | null;
          full_name: string;
          nationality?: string | null;
          gender?: string | null;
          date_of_death: string;
          place_of_death?: string | null;
          place_of_burial?: string | null;
          family_name?: string | null;
          family_email?: string | null;
          family_phone?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          ditib_member_id?: string | null;
          full_name?: string;
          nationality?: string | null;
          gender?: string | null;
          date_of_death?: string;
          place_of_death?: string | null;
          place_of_burial?: string | null;
          family_name?: string | null;
          family_email?: string | null;
          family_phone?: string | null;
          created_at?: string;
        };
      };
      cases: {
        Row: {
          id: string;
          deceased_id: string | null;
          family_user_id: string | null;
          status: 'OPEN' | 'CLOSED' | 'CANCELLED';
          burial_type: 'DE' | 'TR' | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          deceased_id?: string | null;
          family_user_id?: string | null;
          status?: 'OPEN' | 'CLOSED' | 'CANCELLED';
          burial_type?: 'DE' | 'TR' | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          deceased_id?: string | null;
          family_user_id?: string | null;
          status?: 'OPEN' | 'CLOSED' | 'CANCELLED';
          burial_type?: 'DE' | 'TR' | null;
          created_at?: string;
        };
      };
      tasks: {
        Row: {
          id: string;
          case_id: string;
          assignee_id: string | null;
          created_by_id: string | null;
          task_type: 'PICK_UP_FROM_MORGUE' | 'TO_AIRPORT' | 'TO_CONSULATE' | 'DELIVERED' | 'DOCUMENT_DELIVERY' | 'FAMILY_MEETING' | 'HOSPITAL_TRANSFER' | 'BURIAL_PREPARATION' | 'CEREMONY_SETUP' | 'TRANSPORT';
          title: string;
          description: string | null;
          status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED' | 'OVERDUE' | 'CANCELLED';

          priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
          progress: number;
          estimated_duration: string | null;
          scheduled_at: string | null;
          due_time: string | null;
          started_at: string | null;
          completed_at: string | null;
          location: string | null;
          pickup_location: string | null;
          delivery_location: string | null;
          current_latitude: number | null;
          current_longitude: number | null;
          notes: string | null;
          case_name: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          case_id: string;
          assignee_id?: string | null;
          task_type: 'PICK_UP_FROM_MORGUE' | 'TO_AIRPORT' | 'TO_CONSULATE' | 'DELIVERED';
          status?: 'PENDING' | 'ACTIVE' | 'DONE' | 'FAILED' | 'OVERDUE';
          sub_status?: 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 'ON_HOLD' | null;
          scheduled_at?: string | null;
          started_at?: string | null;
          completed_at?: string | null;
          notes?: string | null;
        };
        Update: {
          id?: string;
          case_id?: string;
          assignee_id?: string | null;
          task_type?: 'PICK_UP_FROM_MORGUE' | 'TO_AIRPORT' | 'TO_CONSULATE' | 'DELIVERED';
          status?: 'PENDING' | 'ACTIVE' | 'DONE' | 'FAILED' | 'OVERDUE';
          sub_status?: 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 'ON_HOLD' | null;
          scheduled_at?: string | null;
          started_at?: string | null;
          completed_at?: string | null;
          notes?: string | null;
        };
      };
      task_status_history: {
        Row: {
          id: string;
          task_id: string;
          actor_id: string | null;
          old_status: string | null;
          new_status: string | null;
          old_sub_status: string | null;
          new_sub_status: string | null;
          old_priority: string | null;
          new_priority: string | null;
          old_progress: number | null;
          new_progress: number | null;
          location_latitude: number | null;
          location_longitude: number | null;
          location_description: string | null;
          change_reason: string | null;
          notes: string | null;
          changed_at: string;
          device_info: any | null;
        };
        Insert: {
          id?: string;
          task_id: string;
          actor_id?: string | null;
          old_status?: string | null;
          new_status?: string | null;
          old_sub_status?: string | null;
          new_sub_status?: string | null;
          old_priority?: string | null;
          new_priority?: string | null;
          old_progress?: number | null;
          new_progress?: number | null;
          location_latitude?: number | null;
          location_longitude?: number | null;
          location_description?: string | null;
          change_reason?: string | null;
          notes?: string | null;
          changed_at?: string;
          device_info?: any | null;
        };
        Update: {
          id?: string;
          task_id?: string;
          actor_id?: string | null;
          old_status?: string | null;
          new_status?: string | null;
          old_sub_status?: string | null;
          new_sub_status?: string | null;
          old_priority?: string | null;
          new_priority?: string | null;
          old_progress?: number | null;
          new_progress?: number | null;
          location_latitude?: number | null;
          location_longitude?: number | null;
          location_description?: string | null;
          change_reason?: string | null;
          notes?: string | null;
          changed_at?: string;
          device_info?: any | null;
        };
      };
      documents: {
        Row: {
          id: string;
          case_id: string;
          doc_type: 'DEATH_CERT' | 'FORMUL_C' | 'CARGO_WAYBILL' | 'PHOTO';
          storage_path: string;
          uploaded_by: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          case_id: string;
          doc_type: 'DEATH_CERT' | 'FORMUL_C' | 'CARGO_WAYBILL' | 'PHOTO';
          storage_path: string;
          uploaded_by?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          case_id?: string;
          doc_type?: 'DEATH_CERT' | 'FORMUL_C' | 'CARGO_WAYBILL' | 'PHOTO';
          storage_path?: string;
          uploaded_by?: string | null;
          created_at?: string;
        };
      };
      notifications: {
        Row: {
          id: string;
          case_id: string | null;
          channel: 'PUSH' | 'SMS' | 'EMAIL';
          to_address: string;
          template: string;
          sent_at: string | null;
          status: 'SENT' | 'FAIL' | null;
        };
        Insert: {
          id?: string;
          case_id?: string | null;
          channel: 'PUSH' | 'SMS' | 'EMAIL';
          to_address: string;
          template: string;
          sent_at?: string | null;
          status?: 'SENT' | 'FAIL' | null;
        };
        Update: {
          id?: string;
          case_id?: string | null;
          channel?: 'PUSH' | 'SMS' | 'EMAIL';
          to_address?: string;
          template?: string;
          sent_at?: string | null;
          status?: 'SENT' | 'FAIL' | null;
        };
      };
      lookup_values: {
        Row: {
          id: number;
          category: string;
          code: string;
          label_tr: string;
          label_de: string | null;
        };
        Insert: {
          id?: number;
          category: string;
          code: string;
          label_tr: string;
          label_de?: string | null;
        };
        Update: {
          id?: number;
          category?: string;
          code?: string;
          label_tr?: string;
          label_de?: string | null;
        };
      };
    };
  };
}

// Typed Supabase Client
export type SupabaseClient = typeof supabase;
