import { supabase } from '../lib/supabase';

export interface CaseData {
  id: string;
  deceased_id: string;
  family_user_id?: string;
  status: 'OPEN' | 'CLOSED' | 'CANCELLED';
  burial_type: 'DE' | 'TR';
  created_at: string;
  deceased?: {
    id: string;
    full_name: string;
    nationality: string;
    gender: string;
    date_of_death: string;
    place_of_death: string;
    place_of_burial: string;
    family_name: string;
    family_email: string;
    family_phone: string;
  };
  family_user?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
  tasks?: TaskData[];
}

export interface TaskData {
  id: string;
  case_id: string;
  assignee_id?: string;
  task_type: 'PICK_UP_FROM_MORGUE' | 'TO_AIRPORT' | 'TO_CONSULATE' | 'DELIVERED';
  status: 'PENDING' | 'ACTIVE' | 'DONE' | 'FAILED' | 'OVERDUE';
  sub_status?: 'PICKED_UP' | 'IN_TRANSIT' | 'DELIVERED' | 'ON_HOLD';
  scheduled_at?: string;
  started_at?: string;
  completed_at?: string;
  notes?: string;
  assignee?: {
    id: string;
    full_name: string;
    email: string;
    phone?: string;
  };
}

class CaseService {
  async getAllCases(): Promise<CaseData[]> {
    try {
      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          deceased:deceased_id (
            id,
            full_name,
            nationality,
            gender,
            date_of_death,
            place_of_death,
            place_of_burial,
            family_name,
            family_email,
            family_phone
          ),
          family_user:family_user_id (
            id,
            full_name,
            email,
            phone
          ),
          tasks (
            id,
            case_id,
            assignee_id,
            task_type,
            status,
            sub_status,
            scheduled_at,
            started_at,
            completed_at,
            notes,
            assignee:assignee_id (
              id,
              full_name,
              email,
              phone
            )
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching cases:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('CaseService.getAllCases error:', error);
      throw error;
    }
  }

  async getCasesByDriverId(driverId: string): Promise<CaseData[]> {
    try {
      // First get case IDs that have tasks assigned to this driver
      const { data: taskData, error: taskError } = await supabase
        .from('tasks')
        .select('case_id')
        .eq('assignee_id', driverId);

      if (taskError) {
        console.error('Error fetching driver tasks:', taskError);
        throw taskError;
      }

      if (!taskData || taskData.length === 0) {
        return [];
      }

      const caseIds = taskData.map(task => task.case_id);

      // Then get the cases with those IDs
      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          deceased:deceased_id (
            id,
            full_name,
            nationality,
            gender,
            date_of_death,
            place_of_death,
            place_of_burial,
            family_name,
            family_email,
            family_phone
          ),
          family_user:family_user_id (
            id,
            full_name,
            email,
            phone
          ),
          tasks (
            id,
            case_id,
            assignee_id,
            task_type,
            status,
            sub_status,
            scheduled_at,
            started_at,
            completed_at,
            notes,
            assignee:assignee_id (
              id,
              full_name,
              email,
              phone
            )
          )
        `)
        .in('id', caseIds)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching driver cases:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('CaseService.getCasesByDriverId error:', error);
      throw error;
    }
  }

  async getCasesByFamilyEmail(familyEmail: string): Promise<CaseData[]> {
    try {
      // First get the family user by email
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id')
        .eq('email', familyEmail)
        .eq('role', 'FAMILY')
        .single();

      if (userError || !userData) {
        console.error('Error fetching family user:', userError);
        return [];
      }

      // Then get cases for this family user
      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          deceased:deceased_id (
            id,
            full_name,
            nationality,
            gender,
            date_of_death,
            place_of_death,
            place_of_burial,
            family_name,
            family_email,
            family_phone
          ),
          family_user:family_user_id (
            id,
            full_name,
            email,
            phone
          ),
          tasks (
            id,
            case_id,
            assignee_id,
            task_type,
            status,
            sub_status,
            scheduled_at,
            started_at,
            completed_at,
            notes,
            assignee:assignee_id (
              id,
              full_name,
              email,
              phone
            )
          )
        `)
        .eq('family_user_id', userData.id)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching family cases:', error);
        throw error;
      }

      return data || [];
    } catch (error) {
      console.error('CaseService.getCasesByFamilyEmail error:', error);
      throw error;
    }
  }

  async getCaseById(caseId: string): Promise<CaseData | null> {
    try {
      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          deceased:deceased_id (
            id,
            full_name,
            nationality,
            gender,
            date_of_death,
            place_of_death,
            place_of_burial,
            family_name,
            family_email,
            family_phone
          ),
          family_user:family_user_id (
            id,
            full_name,
            email,
            phone
          ),
          tasks (
            id,
            case_id,
            assignee_id,
            task_type,
            status,
            sub_status,
            scheduled_at,
            started_at,
            completed_at,
            notes,
            assignee:assignee_id (
              id,
              full_name,
              email,
              phone
            )
          )
        `)
        .eq('id', caseId)
        .single();

      if (error) {
        console.error('Error fetching case:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('CaseService.getCaseById error:', error);
      throw error;
    }
  }

  async updateCaseStatus(caseId: string, status: CaseData['status']): Promise<void> {
    try {
      const { error } = await supabase
        .from('cases')
        .update({ status })
        .eq('id', caseId);

      if (error) {
        console.error('Error updating case status:', error);
        throw error;
      }
    } catch (error) {
      console.error('CaseService.updateCaseStatus error:', error);
      throw error;
    }
  }

  async assignDriverToTask(taskId: string, driverId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('tasks')
        .update({ assignee_id: driverId })
        .eq('id', taskId);

      if (error) {
        console.error('Error assigning driver to task:', error);
        throw error;
      }
    } catch (error) {
      console.error('CaseService.assignDriverToTask error:', error);
      throw error;
    }
  }
}

export const caseService = new CaseService();
