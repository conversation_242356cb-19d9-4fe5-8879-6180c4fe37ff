import { supabase } from '../lib/supabase';

export interface SimpleCaseData {
  id: string;
  deceased_id: string;
  family_user_id?: string;
  status: 'OPEN' | 'CLOSED' | 'CANCELLED';
  burial_type: 'DE' | 'TR';
  created_at: string;
}

class SimpleCaseService {
  async getAllCases(): Promise<SimpleCaseData[]> {
    try {
      console.log('SimpleCaseService: Fetching all cases...');

      const { data, error } = await supabase
        .from('cases')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('SimpleCaseService: Error fetching cases:', error);
        throw error;
      }

      console.log('SimpleCaseService: Successfully fetched cases:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('SimpleCaseService.getAllCases error:', error);
      throw error;
    }
  }

  async getCasesWithDeceased(): Promise<any[]> {
    try {
      console.log('SimpleCaseService: Fetching cases with deceased...');

      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          deceased:deceased_id (
            id,
            full_name,
            nationality,
            gender,
            date_of_death,
            place_of_death,
            place_of_burial,
            family_name,
            family_email,
            family_phone
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('SimpleCaseService: Error fetching cases with deceased:', error);
        throw error;
      }

      console.log('SimpleCaseService: Successfully fetched cases with deceased:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('SimpleCaseService.getCasesWithDeceased error:', error);
      throw error;
    }
  }

  async getCasesWithTasks(): Promise<any[]> {
    try {
      console.log('SimpleCaseService: Fetching cases with tasks...');

      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          tasks (
            id,
            case_id,
            assignee_id,
            task_type,
            status,
            sub_status,
            scheduled_at,
            started_at,
            completed_at,
            notes
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('SimpleCaseService: Error fetching cases with tasks:', error);
        throw error;
      }

      console.log('SimpleCaseService: Successfully fetched cases with tasks:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('SimpleCaseService.getCasesWithTasks error:', error);
      throw error;
    }
  }

  async getCasesWithAll(): Promise<any[]> {
    try {
      console.log('SimpleCaseService: Fetching cases with all relations...');

      const { data, error } = await supabase
        .from('cases')
        .select(`
          *,
          deceased:deceased_id (
            id,
            full_name,
            nationality,
            gender,
            date_of_death,
            place_of_death,
            place_of_burial,
            family_name,
            family_email,
            family_phone
          ),
          family_user:family_user_id (
            id,
            full_name,
            email,
            phone
          ),
          tasks (
            id,
            case_id,
            assignee_id,
            task_type,
            status,
            sub_status,
            scheduled_at,
            started_at,
            completed_at,
            notes
          )
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('SimpleCaseService: Error fetching cases with all relations:', error);
        throw error;
      }

      console.log('SimpleCaseService: Successfully fetched cases with all relations:', data?.length || 0);
      return data || [];
    } catch (error) {
      console.error('SimpleCaseService.getCasesWithAll error:', error);
      throw error;
    }
  }
}

export const simpleCaseService = new SimpleCaseService();
