// OPTIMIZE EDİLMİŞ TYPESCRIPT INTERFACE'LERİ
// Veritabanı tablolarıyla tam uyumlu veri yapıları
// Tarih: 2024-01-20
// Versiyon: 1.0

// =====================================================
// BASE INTERFACES
// =====================================================

export interface BaseEntity {
  id: string;
  created_at: string;
  updated_at: string;
}

export interface LocationData {
  latitude: number;
  longitude: number;
}

// =====================================================
// USER INTERFACES
// =====================================================

export type UserRole = 'ADMIN' | 'DRIVER' | 'FAMILY';
export type UserStatus = 'ACTIVE' | 'ON_LEAVE' | 'INACTIVE';

export interface User extends BaseEntity {
  role: UserRole;
  full_name: string;
  email: string;
  phone?: string;
  status: UserStatus;
  profile_image_url?: string;
  last_login_at?: string;
}

// =====================================================
// DECEASED INTERFACES
// =====================================================

export type Gender = 'MALE' | 'FEMALE';

export interface Deceased extends BaseEntity {
  ditib_member_id?: string;
  full_name: string;
  nationality: string;
  gender?: Gender;
  date_of_birth?: string;
  date_of_death: string;
  place_of_death?: string;
  place_of_burial?: string;
  burial_location_latitude?: number;
  burial_location_longitude?: number;
  family_contact_name?: string;
  family_email?: string;
  family_phone?: string;
  family_address?: string;
}

// =====================================================
// CASE INTERFACES
// =====================================================

export type CaseStatus = 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CLOSED' | 'CANCELLED';
export type Priority = 'URGENT' | 'HIGH' | 'MEDIUM' | 'LOW';
export type BurialType = 'DE' | 'TR' | 'TRADITIONAL' | 'MODERN';
export type BurialCeremonyType = 'RELIGIOUS' | 'CIVIL' | 'MIXED';

export interface Case extends BaseEntity {
  deceased_id: string;
  family_user_id?: string;
  assigned_driver_id?: string;
  status: CaseStatus;
  priority: Priority;
  burial_type?: BurialType;
  burial_ceremony_type?: BurialCeremonyType;
  progress_percentage: number;
  estimated_completion_date?: string;
  total_tasks_count: number;
  completed_tasks_count: number;
  pending_tasks_count: number;
  notes?: string;
  special_instructions?: string;
  
  // İlişkili veriler (JOIN'ler için)
  deceased?: Deceased;
  family_user?: User;
  assigned_driver?: User;
}

// =====================================================
// TASK INTERFACES
// =====================================================

export type TaskType = 
  | 'PICK_UP_FROM_MORGUE'
  | 'TO_AIRPORT'
  | 'TO_CONSULATE'
  | 'DELIVERED'
  | 'DOCUMENT_DELIVERY'
  | 'FAMILY_MEETING'
  | 'HOSPITAL_TRANSFER'
  | 'BURIAL_PREPARATION'
  | 'CEREMONY_SETUP'
  | 'TRANSPORT';

export type TaskStatus = 
  | 'PENDING'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'FAILED'
  | 'OVERDUE'
  | 'CANCELLED';

export type TaskSubStatus = 
  | 'PICKED_UP'
  | 'IN_TRANSIT'
  | 'DELIVERED'
  | 'ON_HOLD'
  | 'WAITING_APPROVAL'
  | 'READY_FOR_PICKUP'
  | 'ARRIVED_AT_DESTINATION';

export interface Task extends BaseEntity {
  case_id: string;
  assignee_id?: string;
  created_by_id?: string;
  
  // Görev bilgileri
  task_type: TaskType;
  title: string;
  description?: string;
  
  // Durum ve öncelik
  status: TaskStatus;
  sub_status?: TaskSubStatus;
  priority: Priority;
  
  // Zaman bilgileri
  scheduled_at?: string;
  due_at?: string;
  started_at?: string;
  completed_at?: string;
  estimated_duration_minutes?: number;
  actual_duration_minutes?: number;
  
  // Konum bilgileri
  location?: string;
  pickup_address?: string;
  delivery_address?: string;
  pickup_latitude?: number;
  pickup_longitude?: number;
  delivery_latitude?: number;
  delivery_longitude?: number;
  
  // İlerleme takibi
  progress_percentage: number;
  
  // Notlar ve feedback
  notes?: string;
  completion_notes?: string;
  driver_feedback?: string;
  
  // İlişkili veriler (JOIN'ler için)
  case?: Case;
  assignee?: User;
  created_by?: User;
}

// =====================================================
// DOCUMENT INTERFACES
// =====================================================

export type DocumentType = 
  | 'DEATH_CERT'
  | 'FORMUL_C'
  | 'CARGO_WAYBILL'
  | 'PHOTO'
  | 'DOCUMENT_PHOTO'
  | 'TRANSPORT_DOCUMENT'
  | 'NOTES'
  | 'FAMILY_CONSENT'
  | 'MEDICAL_REPORT'
  | 'BURIAL_PERMIT';

export type DocumentStatus = 'PENDING' | 'APPROVED' | 'REJECTED' | 'UNDER_REVIEW';

export interface Document extends BaseEntity {
  case_id: string;
  task_id?: string;
  uploaded_by_id?: string;
  
  // Belge bilgileri
  document_type: DocumentType;
  title: string;
  description?: string;
  
  // Dosya bilgileri
  file_name: string;
  file_size_bytes?: number;
  file_mime_type?: string;
  storage_path: string;
  file_count: number;
  
  // Durum takibi
  status: DocumentStatus;
  reviewed_by_id?: string;
  reviewed_at?: string;
  rejection_reason?: string;
  
  // İlişkili veriler (JOIN'ler için)
  case?: Case;
  task?: Task;
  uploaded_by?: User;
  reviewed_by?: User;
}

// =====================================================
// TASK STATUS HISTORY INTERFACES
// =====================================================

export interface TaskStatusHistory {
  id: string;
  task_id: string;
  actor_id?: string;
  
  // Durum değişiklikleri
  old_status?: string;
  new_status?: string;
  old_sub_status?: string;
  new_sub_status?: string;
  old_progress?: number;
  new_progress?: number;
  
  // Konum bilgisi
  location_latitude?: number;
  location_longitude?: number;
  
  // Notlar
  change_reason?: string;
  notes?: string;
  
  // Metadata
  changed_at: string;
  
  // İlişkili veriler
  task?: Task;
  actor?: User;
}

// =====================================================
// NOTIFICATION INTERFACES
// =====================================================

export type NotificationType = 
  | 'TASK_ASSIGNED'
  | 'TASK_COMPLETED'
  | 'TASK_OVERDUE'
  | 'CASE_UPDATED'
  | 'DOCUMENT_UPLOADED'
  | 'DOCUMENT_APPROVED'
  | 'DOCUMENT_REJECTED'
  | 'FAMILY_MESSAGE'
  | 'SYSTEM_ALERT';

export type NotificationChannel = 'PUSH' | 'SMS' | 'EMAIL' | 'IN_APP';
export type NotificationStatus = 'PENDING' | 'SENT' | 'DELIVERED' | 'FAILED';

export interface Notification {
  id: string;
  case_id?: string;
  task_id?: string;
  user_id?: string;
  
  // Bildirim bilgileri
  notification_type: NotificationType;
  channel: NotificationChannel;
  
  // İçerik
  title: string;
  message: string;
  to_address: string;
  
  // Durum
  status: NotificationStatus;
  sent_at?: string;
  delivered_at?: string;
  read_at?: string;
  
  // Metadata
  created_at: string;
  
  // İlişkili veriler
  case?: Case;
  task?: Task;
  user?: User;
}

// =====================================================
// LOOKUP VALUES INTERFACES
// =====================================================

export interface LookupValue {
  id: number;
  category: string;
  code: string;
  label_tr: string;
  label_de?: string;
  label_en?: string;
  sort_order: number;
  is_active: boolean;
}

// =====================================================
// VIEW INTERFACES (Database Views)
// =====================================================

export interface DriverTaskView {
  id: string;
  case_id: string;
  title: string;
  description?: string;
  task_type: TaskType;
  status: TaskStatus;
  sub_status?: TaskSubStatus;
  priority: Priority;
  scheduled_at?: string;
  due_at?: string;
  location?: string;
  progress_percentage: number;
  estimated_duration_minutes?: number;
  case_status: CaseStatus;
  deceased_name: string;
  family_contact_name?: string;
  family_phone?: string;
  assignee_name?: string;
}

export interface CaseSummaryView {
  id: string;
  status: CaseStatus;
  priority: Priority;
  burial_type?: BurialType;
  progress_percentage: number;
  total_tasks_count: number;
  completed_tasks_count: number;
  pending_tasks_count: number;
  estimated_completion_date?: string;
  deceased_name: string;
  date_of_death: string;
  family_contact_name?: string;
  family_phone?: string;
  family_email?: string;
  family_user_name?: string;
  assigned_driver_name?: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentSummaryView {
  id: string;
  case_id: string;
  task_id?: string;
  document_type: DocumentType;
  title: string;
  file_name: string;
  file_size_bytes?: number;
  file_count: number;
  status: DocumentStatus;
  rejection_reason?: string;
  created_at: string;
  case_status: CaseStatus;
  deceased_name: string;
  uploaded_by_name?: string;
  reviewed_by_name?: string;
}

// =====================================================
// API RESPONSE INTERFACES
// =====================================================

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// =====================================================
// FORM INTERFACES (UI Components)
// =====================================================

export interface TaskFormData {
  title: string;
  description?: string;
  task_type: TaskType;
  priority: Priority;
  scheduled_at?: string;
  due_at?: string;
  estimated_duration_minutes?: number;
  location?: string;
  pickup_address?: string;
  delivery_address?: string;
  notes?: string;
}

export interface CaseFormData {
  deceased_id: string;
  family_user_id?: string;
  assigned_driver_id?: string;
  priority: Priority;
  burial_type?: BurialType;
  burial_ceremony_type?: BurialCeremonyType;
  estimated_completion_date?: string;
  notes?: string;
  special_instructions?: string;
}

export interface DocumentFormData {
  case_id: string;
  task_id?: string;
  document_type: DocumentType;
  title: string;
  description?: string;
  file: File;
}

// =====================================================
// FILTER INTERFACES (Search & Filter)
// =====================================================

export interface TaskFilter {
  status?: TaskStatus[];
  priority?: Priority[];
  task_type?: TaskType[];
  assignee_id?: string;
  case_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CaseFilter {
  status?: CaseStatus[];
  priority?: Priority[];
  burial_type?: BurialType[];
  assigned_driver_id?: string;
  family_user_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface DocumentFilter {
  document_type?: DocumentType[];
  status?: DocumentStatus[];
  case_id?: string;
  uploaded_by_id?: string;
  date_from?: string;
  date_to?: string;
  search?: string;
}

// =====================================================
// STATISTICS INTERFACES
// =====================================================

export interface TaskStatistics {
  total: number;
  pending: number;
  in_progress: number;
  completed: number;
  overdue: number;
  failed: number;
  cancelled: number;
}

export interface CaseStatistics {
  total: number;
  open: number;
  in_progress: number;
  completed: number;
  closed: number;
  cancelled: number;
}

export interface DocumentStatistics {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  under_review: number;
}

export interface DashboardStatistics {
  tasks: TaskStatistics;
  cases: CaseStatistics;
  documents: DocumentStatistics;
  active_drivers: number;
  pending_notifications: number;
}

// =====================================================
// LOCATION & PROGRESS INTERFACES
// =====================================================

export interface TaskProgressCalculation {
  task_id: string;
  current_location: LocationData;
  target_location: LocationData;
  calculated_progress: number;
  distance_remaining: number;
  is_near_target: boolean;
  estimated_arrival_time?: string;
}

export interface LocationUpdate {
  task_id: string;
  latitude: number;
  longitude: number;
  accuracy?: number;
  timestamp: string;
  speed?: number;
  heading?: number;
}

// =====================================================
// UTILITY TYPES
// =====================================================

export type CreateTaskData = Omit<Task, 'id' | 'created_at' | 'updated_at' | 'progress_percentage'>;
export type UpdateTaskData = Partial<Omit<Task, 'id' | 'created_at' | 'case_id'>>;

export type CreateCaseData = Omit<Case, 'id' | 'created_at' | 'updated_at' | 'progress_percentage' | 'total_tasks_count' | 'completed_tasks_count' | 'pending_tasks_count'>;
export type UpdateCaseData = Partial<Omit<Case, 'id' | 'created_at' | 'deceased_id'>>;

export type CreateDocumentData = Omit<Document, 'id' | 'created_at' | 'updated_at' | 'status' | 'reviewed_by_id' | 'reviewed_at'>;
export type UpdateDocumentData = Partial<Omit<Document, 'id' | 'created_at' | 'case_id' | 'file_name' | 'storage_path'>>;

// =====================================================
// CONSTANTS
// =====================================================

export const TASK_TYPE_LABELS: Record<TaskType, string> = {
  PICK_UP_FROM_MORGUE: 'Morgue\'dan Alma',
  TO_AIRPORT: 'Havaalanına Nakil',
  TO_CONSULATE: 'Konsolosluğa Nakil',
  DELIVERED: 'Teslim',
  DOCUMENT_DELIVERY: 'Belge Teslimi',
  FAMILY_MEETING: 'Aile Görüşmesi',
  HOSPITAL_TRANSFER: 'Hastane Nakli',
  BURIAL_PREPARATION: 'Defin Hazırlığı',
  CEREMONY_SETUP: 'Tören Hazırlığı',
  TRANSPORT: 'Nakil'
};

export const TASK_STATUS_LABELS: Record<TaskStatus, string> = {
  PENDING: 'Bekliyor',
  IN_PROGRESS: 'Devam Ediyor',
  COMPLETED: 'Tamamlandı',
  FAILED: 'Başarısız',
  OVERDUE: 'Gecikmiş',
  CANCELLED: 'İptal Edildi'
};

export const PRIORITY_LABELS: Record<Priority, string> = {
  URGENT: 'Acil',
  HIGH: 'Yüksek',
  MEDIUM: 'Orta',
  LOW: 'Düşük'
};

export const CASE_STATUS_LABELS: Record<CaseStatus, string> = {
  OPEN: 'Açık',
  IN_PROGRESS: 'Devam Ediyor',
  COMPLETED: 'Tamamlandı',
  CLOSED: 'Kapalı',
  CANCELLED: 'İptal Edildi'
};

export const DOCUMENT_TYPE_LABELS: Record<DocumentType, string> = {
  DEATH_CERT: 'Ölüm Belgesi',
  FORMUL_C: 'Formül C',
  CARGO_WAYBILL: 'Kargo Sevk İrsaliyesi',
  PHOTO: 'Fotoğraf',
  DOCUMENT_PHOTO: 'Belge Fotoğrafı',
  TRANSPORT_DOCUMENT: 'Nakil Belgesi',
  NOTES: 'Notlar',
  FAMILY_CONSENT: 'Aile Onayı',
  MEDICAL_REPORT: 'Tıbbi Rapor',
  BURIAL_PERMIT: 'Defin İzni'
};

export const DOCUMENT_STATUS_LABELS: Record<DocumentStatus, string> = {
  PENDING: 'Bekliyor',
  APPROVED: 'Onaylandı',
  REJECTED: 'Reddedildi',
  UNDER_REVIEW: 'İnceleniyor'
};