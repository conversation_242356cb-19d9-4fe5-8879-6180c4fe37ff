-- ================================
-- DROP ALL TABLES AND TYPES
-- ================================
-- This file drops all existing tables, functions, triggers, and types
-- Use this to completely reset the database before running schema
-- Created: 2025-01-27

-- ================================
-- 1. DROP ALL TRIGGERS FIRST
-- ================================

-- Drop triggers if they exist
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
DROP TRIGGER IF EXISTS update_users_updated_at ON users;
DROP TRIGGER IF EXISTS update_cases_updated_at ON cases;
DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
DROP TRIGGER IF EXISTS update_documents_updated_at ON documents;
DROP TRIGGER IF EXISTS update_notifications_updated_at ON notifications;

-- ================================
-- 2. DROP ALL FUNCTIONS
-- ================================

-- Drop functions if they exist
DROP FUNCTION IF EXISTS handle_new_user() CASCADE;
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;
DROP FUNCTION IF EXISTS get_user_cases(UUID) CASCADE;
DROP FUNCTION IF EXISTS get_driver_tasks(UUID) CASCADE;
DROP FUNCTION IF EXISTS update_task_status(UUID, task_status) CASCADE;

-- ================================
-- 3. DROP ALL TABLES (in reverse dependency order)
-- ================================

-- Drop tables that depend on other tables first
DROP TABLE IF EXISTS task_events CASCADE;
DROP TABLE IF EXISTS notifications CASCADE;
DROP TABLE IF EXISTS documents CASCADE;
DROP TABLE IF EXISTS tasks CASCADE;
DROP TABLE IF EXISTS cases CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- ================================
-- 4. DROP ALL CUSTOM TYPES
-- ================================

-- Drop all enum types (in reverse dependency order)
DROP TYPE IF EXISTS task_event_type CASCADE;
DROP TYPE IF EXISTS task_status CASCADE;
DROP TYPE IF EXISTS task_type CASCADE;
DROP TYPE IF EXISTS place_type CASCADE;
DROP TYPE IF EXISTS notification_status CASCADE;
DROP TYPE IF EXISTS notification_channel CASCADE;
DROP TYPE IF EXISTS document_type CASCADE;
DROP TYPE IF EXISTS burial_type CASCADE;
DROP TYPE IF EXISTS case_status CASCADE;
DROP TYPE IF EXISTS user_status CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;

-- ================================
-- 5. DROP RLS POLICIES (if any remain)
-- ================================

-- Note: Policies are automatically dropped when tables are dropped
-- But we can explicitly drop them if needed

-- ================================
-- 6. RESET SEQUENCES (if any)
-- ================================

-- Drop any custom sequences if they exist
-- (UUIDs don't use sequences, but just in case)

-- ================================
-- COMPLETION MESSAGE
-- ================================

-- This will show in the query result
SELECT 'All tables, types, functions, and triggers have been dropped successfully!' as status;
