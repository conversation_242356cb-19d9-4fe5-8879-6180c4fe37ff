-- ================================
-- FUNERAL APP DATABASE SCHEMA
-- ================================
-- Based on DATABASE-SYSTEM.md specifications
-- Created: 2025-01-27

-- ================================
-- 1. ENUM TYPES
-- ================================

-- Drop existing types if they exist to avoid conflicts
DROP TYPE IF EXISTS task_event_type CASCADE;
DROP TYPE IF EXISTS task_status CASCADE;
DROP TYPE IF EXISTS task_type CASCADE;
DROP TYPE IF EXISTS place_type CASCADE;
DROP TYPE IF EXISTS notification_status CASCADE;
DROP TYPE IF EXISTS notification_channel CASCADE;
DROP TYPE IF EXISTS document_type CASCADE;
DROP TYPE IF EXISTS burial_type CASCADE;
DROP TYPE IF EXISTS case_status CASCADE;
DROP TYPE IF EXISTS user_status CASCADE;
DROP TYPE IF EXISTS user_role CASCADE;

-- 1.1. User Roles and Status
CREATE TYPE user_role AS ENUM (
  'ADMIN',
  'DRIVER',
  'FAMILY'
);

CREATE TYPE user_status AS ENUM (
  'ACTIVE',
  'ON_LEAVE',
  'INACTIVE'
);

-- 1.2. Case Status and Burial Type
CREATE TYPE case_status AS ENUM (
  'OPEN',
  'CLOSED',
  'CANCELLED'
);

CREATE TYPE burial_type AS ENUM (
  'DE',    -- Germany (Deutschland)
  'TR'     -- Turkey
);

-- 1.3. Document Types
CREATE TYPE document_type AS ENUM (
  'DEATH_CERT',     -- Death certificate
  'FORMUL_C',       -- Form C document
  'CARGO_WAYBILL',  -- Cargo waybill
  'PHOTO'           -- Photo etc.
);

-- 1.4. Notification Channel and Status
CREATE TYPE notification_channel AS ENUM (
  'PUSH',
  'SMS',
  'EMAIL'
);

CREATE TYPE notification_status AS ENUM (
  'SENT',
  'FAIL'
);

-- 1.5. Place Types
CREATE TYPE place_type AS ENUM (
  'MORGUE',
  'HOME',
  'AIRPORT',
  'CONSULATE',
  'CEMETERY',
  'OTHER'
);

-- 1.6. Task Type and Status
CREATE TYPE task_type AS ENUM (
  'PICK_UP_FROM_MORGUE',  -- Pick up from morgue
  'PICK_UP_FROM_HOME',    -- Pick up from home
  'TO_AIRPORT',           -- Transport to airport
  'TO_CONSULATE',         -- Transport to consulate
  'DELIVERED'             -- Delivered to destination
);

CREATE TYPE task_status AS ENUM (
  'ASSIGNED',     -- Assigned, driver hasn't started yet
  'IN_PROGRESS',  -- Driver started, task in progress
  'COMPLETED',    -- Task completed
  'FAILED'        -- Failed (cancelled, returned, etc.)
);

-- 1.7. Task Event Types
CREATE TYPE task_event_type AS ENUM (
  'STATUS_CHANGE',    -- Status change (ASSIGNED→IN_PROGRESS, etc.)
  'HEARTBEAT',        -- Periodic location update
  'ARRIVAL',          -- Arrival at destination
  'MANUAL_OVERRIDE'   -- Admin/driver manual intervention
);

-- =============================
-- 2. CORE TABLES
-- =============================

-- 2.1. users
CREATE TABLE users (
  id          UUID              PRIMARY KEY,
  role        user_role         NOT NULL,
  full_name   TEXT              NOT NULL,
  email       TEXT              NOT NULL UNIQUE,
  phone       TEXT                        NULL,
  status      user_status        NOT NULL DEFAULT 'ACTIVE',
  created_at  TIMESTAMPTZ        NOT NULL DEFAULT now()
);

CREATE INDEX idx_users_role_status ON users (role, status);

-- 2.2. deceased
CREATE TABLE deceased (
  id                UUID              PRIMARY KEY,
  ditib_member_id   TEXT              UNIQUE,
  full_name         TEXT              NOT NULL,
  nationality       TEXT                        NULL,
  gender            TEXT                        NULL,
  date_of_death     DATE              NOT NULL,
  place_of_death    TEXT                        NULL,
  place_of_burial   TEXT                        NULL,
  family_name       TEXT              NOT NULL,
  family_email      TEXT              NOT NULL,
  family_phone      TEXT                        NULL,
  created_at        TIMESTAMPTZ        NOT NULL DEFAULT now()
);

-- 2.3. cases
CREATE TABLE cases (
  id             UUID              PRIMARY KEY,
  deceased_id    UUID              NOT NULL REFERENCES deceased(id) ON DELETE CASCADE,
  family_user_id UUID              NOT NULL REFERENCES users(id)     ON DELETE RESTRICT,
  status         case_status       NOT NULL DEFAULT 'OPEN',
  burial_type    burial_type       NOT NULL,
  created_at     TIMESTAMPTZ        NOT NULL DEFAULT now()
);

CREATE INDEX idx_cases_family_user ON cases (family_user_id);

-- 2.4. documents
CREATE TABLE documents (
  id            UUID              PRIMARY KEY,
  case_id       UUID              NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  doc_type      document_type     NOT NULL,
  storage_path  TEXT              NOT NULL,
  uploaded_by   UUID              NOT NULL REFERENCES users(id),
  created_at    TIMESTAMPTZ        NOT NULL DEFAULT now()
);

CREATE INDEX idx_documents_case ON documents (case_id);

-- 2.5. notifications
CREATE TABLE notifications (
  id            UUID                     PRIMARY KEY,
  case_id       UUID        NOT NULL      REFERENCES cases(id) ON DELETE CASCADE,
  channel       notification_channel NOT NULL,
  recipient     TEXT             NOT NULL,
  template      TEXT             NOT NULL,
  sent_at       TIMESTAMPTZ      NOT NULL DEFAULT now(),
  status        notification_status NOT NULL DEFAULT 'SENT'
);

CREATE INDEX idx_notifications_case ON notifications (case_id);

-- 2.6. lookup_values
CREATE TABLE lookup_values (
  id        SERIAL            PRIMARY KEY,
  category  TEXT              NOT NULL,
  code      TEXT              NOT NULL,
  label_tr  TEXT                        NULL,
  label_de  TEXT                        NULL
);

CREATE INDEX idx_lookup_values_category ON lookup_values (category);

-- =============================
-- 3. NEW / UPDATED TABLES
-- =============================

-- 3.1. places
CREATE TABLE places (
  id          UUID            PRIMARY KEY,
  place_type  place_type      NOT NULL,
  name        TEXT            NOT NULL,
  address     TEXT                        NULL,
  lat         NUMERIC(10,7)   NOT NULL,
  lng         NUMERIC(10,7)   NOT NULL,
  country     CHAR(2)         NOT NULL DEFAULT 'TR',
  created_at  TIMESTAMPTZ     NOT NULL DEFAULT now()
);

-- 3.2. tasks
CREATE TABLE tasks (
  id                UUID            PRIMARY KEY,
  case_id           UUID            NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  assignee_id       UUID                        REFERENCES users(id),
  task_type         task_type       NOT NULL,
  status            task_status     NOT NULL DEFAULT 'ASSIGNED',
  origin_place_id   UUID                        REFERENCES places(id),
  dest_place_id     UUID                        REFERENCES places(id),
  scheduled_at      TIMESTAMPTZ      NOT NULL,
  started_at        TIMESTAMPTZ                         NULL,
  completed_at      TIMESTAMPTZ                         NULL,
  est_distance_km   NUMERIC(7,2)                      NULL,
  est_duration_min  INTEGER                           NULL,
  real_distance_km  NUMERIC(7,2)                      NULL,
  real_duration_min INTEGER                           NULL,
  notes             TEXT                              NULL,
  created_at        TIMESTAMPTZ      NOT NULL DEFAULT now()
);

CREATE INDEX idx_tasks_assignee_status ON tasks (assignee_id, status);
CREATE INDEX idx_tasks_case ON tasks (case_id);

-- 3.3. driver_locations
CREATE TABLE driver_locations (
  id                  BIGSERIAL       PRIMARY KEY,
  driver_id           UUID             NOT NULL REFERENCES users(id),
  task_id             UUID             NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  lat                 NUMERIC(10,7)    NOT NULL,
  lng                 NUMERIC(10,7)    NOT NULL,
  recorded_at         TIMESTAMPTZ       NOT NULL DEFAULT now(),
  distance_to_dest_km NUMERIC(7,2)                      NULL,
  eta_min             INTEGER                           NULL
);

CREATE INDEX idx_driver_locations_driver_time 
    ON driver_locations (driver_id, recorded_at DESC);
CREATE INDEX idx_driver_locations_task_time 
    ON driver_locations (task_id, recorded_at DESC);

-- 3.4. task_events
CREATE TABLE task_events (
  id            BIGSERIAL         PRIMARY KEY,
  task_id       UUID              NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  actor_id      UUID              NOT NULL REFERENCES users(id),
  event_type    task_event_type   NOT NULL,
  old_status    task_status                       NULL,
  new_status    task_status                       NULL,
  lat           NUMERIC(10,7)                    NULL,
  lng           NUMERIC(10,7)                    NULL,
  recorded_at   TIMESTAMPTZ        NOT NULL DEFAULT now(),
  extra         JSONB                            NULL
);

CREATE INDEX idx_task_events_task_time ON task_events (task_id, recorded_at DESC);
