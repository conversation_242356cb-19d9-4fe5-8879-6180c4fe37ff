-- ================================
-- SIMPLIFIED RLS POLICIES
-- ================================
-- Circular reference sorununu çözmek için basitleştirilmiş politikalar
-- Created: 2025-01-27

-- Önce mevcut politikaları temizle
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Service role can access all users" ON users;
DROP POLICY IF EXISTS "Authenticated users can read users" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Allow user creation" ON users;

-- Tüm tabloların RLS'ini geçici olarak devre dışı bırak
ALTER TABLE users DISABLE ROW LEVEL SECURITY;
ALTER TABLE deceased DISABLE ROW LEVEL SECURITY;
ALTER TABLE cases DISABLE ROW LEVEL SECURITY;
ALTER TABLE documents DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE lookup_values DISABLE ROW LEVEL SECURITY;
ALTER TABLE places DISABLE ROW LEVEL SECURITY;
ALTER TABLE tasks DISABLE ROW LEVEL SECURITY;
ALTER TABLE driver_locations DISABLE ROW LEVEL SECURITY;
ALTER TABLE task_events DISABLE ROW LEVEL SECURITY;

-- Sadece users tablosu için basit RLS aktif et
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- ================================
-- USERS TABLE - BASIT POLİTİKALAR
-- ================================

-- Herkes users tablosunu okuyabilir (auth kontrolü için gerekli)
CREATE POLICY "Allow read access to users" ON users
  FOR SELECT USING (true);

-- Sadece kendi profilini güncelleyebilir
CREATE POLICY "Users can update own profile" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Yeni kullanıcı oluşturma (trigger ile kontrol edilecek)
CREATE POLICY "Allow user creation" ON users
  FOR INSERT WITH CHECK (true);

-- Silme işlemi sadece service role
CREATE POLICY "Service role can delete users" ON users
  FOR DELETE USING (auth.jwt() ->> 'role' = 'service_role');

-- ================================
-- DİĞER TABLOLAR İÇİN BASİT KONTROL
-- ================================

-- Lookup values ve places herkese açık (read-only)
ALTER TABLE lookup_values ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow read lookup values" ON lookup_values
  FOR SELECT USING (true);

ALTER TABLE places ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Allow read places" ON places
  FOR SELECT USING (true);

-- Diğer tablolar için temel auth kontrolü
ALTER TABLE deceased ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can access deceased" ON deceased
  FOR ALL USING (auth.uid() IS NOT NULL);

ALTER TABLE cases ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can access cases" ON cases
  FOR ALL USING (auth.uid() IS NOT NULL);

ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can access documents" ON documents
  FOR ALL USING (auth.uid() IS NOT NULL);

ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can access notifications" ON notifications
  FOR ALL USING (auth.uid() IS NOT NULL);

ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can access tasks" ON tasks
  FOR ALL USING (auth.uid() IS NOT NULL);

ALTER TABLE driver_locations ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can access driver_locations" ON driver_locations
  FOR ALL USING (auth.uid() IS NOT NULL);

ALTER TABLE task_events ENABLE ROW LEVEL SECURITY;
CREATE POLICY "Authenticated users can access task_events" ON task_events
  FOR ALL USING (auth.uid() IS NOT NULL);

-- ================================
-- NOTLAR
-- ================================
-- Bu basitleştirilmiş politikalar circular reference sorununu çözer
-- Rol bazlı erişim kontrolü uygulama seviyesinde yapılacak
-- Daha sonra ihtiyaç duyulursa daha detaylı politikalar eklenebilir
