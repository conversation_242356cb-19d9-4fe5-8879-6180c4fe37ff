-- ================================
-- TRIGGER FUNCTIONS
-- ================================
-- Based on DATABASE-SYSTEM.md specifications
-- Created: 2025-01-27

-- ================================
-- 1. USER PROFILE CREATION TRIGGER
-- ================================

-- Function to automatically create user profile when auth.users record is created
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
  existing_user RECORD;
BEGIN
  -- Check if user already exists in users table by email
  SELECT * INTO existing_user FROM public.users WHERE email = NEW.email;

  IF FOUND THEN
    -- User exists in users table, update the ID to match auth.users ID
    UPDATE public.users
    SET id = NEW.id,
        updated_at = NOW()
    WHERE email = NEW.email;
  ELSE
    -- User doesn't exist, create new user with default role as FAMILY
    INSERT INTO public.users (id, role, full_name, email, phone, status, created_at)
    VALUES (
      NEW.id,
      'FAMILY',
      COALESCE(NEW.raw_user_meta_data->>'full_name', 'New User'),
      NEW.email,
      NEW.phone,
      'ACTIVE',
      NOW()
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for auth.users table
CREATE OR REPLACE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- ================================
-- 2. TASK STATUS CHANGE TRIGGER
-- ================================

-- Function to log task status changes in task_events
CREATE OR REPLACE FUNCTION public.log_task_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only log if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    INSERT INTO public.task_events (
      task_id,
      actor_id,
      event_type,
      old_status,
      new_status,
      recorded_at
    ) VALUES (
      NEW.id,
      auth.uid(),
      'STATUS_CHANGE',
      OLD.status,
      NEW.status,
      NOW()
    );
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for tasks table
CREATE OR REPLACE TRIGGER on_task_status_change
  AFTER UPDATE ON public.tasks
  FOR EACH ROW EXECUTE FUNCTION public.log_task_status_change();

-- ================================
-- 3. DISTANCE AND ETA CALCULATION
-- ================================

-- Function to calculate distance using Haversine formula
CREATE OR REPLACE FUNCTION public.calculate_distance(
  lat1 NUMERIC,
  lng1 NUMERIC,
  lat2 NUMERIC,
  lng2 NUMERIC
) RETURNS NUMERIC AS $$
DECLARE
  earth_radius CONSTANT NUMERIC := 6371; -- Earth radius in kilometers
  dlat NUMERIC;
  dlng NUMERIC;
  a NUMERIC;
  c NUMERIC;
BEGIN
  -- Convert degrees to radians
  dlat := radians(lat2 - lat1);
  dlng := radians(lng2 - lng1);
  
  -- Haversine formula
  a := sin(dlat/2) * sin(dlat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlng/2) * sin(dlng/2);
  c := 2 * atan2(sqrt(a), sqrt(1-a));
  
  RETURN earth_radius * c;
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Function to update distance and ETA for driver locations
CREATE OR REPLACE FUNCTION public.update_driver_location_metrics()
RETURNS TRIGGER AS $$
DECLARE
  dest_lat NUMERIC;
  dest_lng NUMERIC;
  distance_km NUMERIC;
  eta_minutes INTEGER;
BEGIN
  -- Get destination coordinates from the task
  SELECT p.lat, p.lng INTO dest_lat, dest_lng
  FROM public.tasks t
  JOIN public.places p ON p.id = t.dest_place_id
  WHERE t.id = NEW.task_id;
  
  -- Calculate distance if destination exists
  IF dest_lat IS NOT NULL AND dest_lng IS NOT NULL THEN
    distance_km := public.calculate_distance(NEW.lat, NEW.lng, dest_lat, dest_lng);
    
    -- Simple ETA calculation (assuming average speed of 50 km/h)
    eta_minutes := ROUND(distance_km * 60 / 50);
    
    -- Update the new record
    NEW.distance_to_dest_km := distance_km;
    NEW.eta_min := eta_minutes;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for driver_locations table
CREATE OR REPLACE TRIGGER on_driver_location_insert
  BEFORE INSERT ON public.driver_locations
  FOR EACH ROW EXECUTE FUNCTION public.update_driver_location_metrics();

-- ================================
-- 4. NOTIFICATION HELPER FUNCTIONS
-- ================================

-- Function to send notification (placeholder for external service integration)
CREATE OR REPLACE FUNCTION public.send_notification(
  p_case_id UUID,
  p_channel notification_channel,
  p_recipient TEXT,
  p_template TEXT
) RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  -- Insert notification record
  INSERT INTO public.notifications (
    id,
    case_id,
    channel,
    recipient,
    template,
    sent_at,
    status
  ) VALUES (
    gen_random_uuid(),
    p_case_id,
    p_channel,
    p_recipient,
    p_template,
    NOW(),
    'SENT'
  ) RETURNING id INTO notification_id;
  
  -- Here you would integrate with external notification service
  -- For now, we just return the notification ID
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================
-- 5. TASK ASSIGNMENT HELPER
-- ================================

-- Function to assign task to driver
CREATE OR REPLACE FUNCTION public.assign_task_to_driver(
  p_task_id UUID,
  p_driver_id UUID,
  p_scheduled_at TIMESTAMPTZ DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  task_exists BOOLEAN;
  driver_exists BOOLEAN;
BEGIN
  -- Check if task exists and is not already assigned
  SELECT EXISTS(
    SELECT 1 FROM public.tasks 
    WHERE id = p_task_id AND (assignee_id IS NULL OR status = 'FAILED')
  ) INTO task_exists;
  
  -- Check if driver exists and is active
  SELECT EXISTS(
    SELECT 1 FROM public.users 
    WHERE id = p_driver_id AND role = 'DRIVER' AND status = 'ACTIVE'
  ) INTO driver_exists;
  
  IF NOT task_exists THEN
    RAISE EXCEPTION 'Task not found or already assigned';
  END IF;
  
  IF NOT driver_exists THEN
    RAISE EXCEPTION 'Driver not found or not active';
  END IF;
  
  -- Update task
  UPDATE public.tasks SET
    assignee_id = p_driver_id,
    scheduled_at = COALESCE(p_scheduled_at, scheduled_at),
    status = 'ASSIGNED',
    started_at = NULL,
    completed_at = NULL
  WHERE id = p_task_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================
-- 6. CASE STATISTICS FUNCTIONS
-- ================================

-- Function to get case statistics
CREATE OR REPLACE FUNCTION public.get_case_statistics()
RETURNS TABLE (
  total_cases BIGINT,
  open_cases BIGINT,
  closed_cases BIGINT,
  cancelled_cases BIGINT,
  total_drivers BIGINT,
  active_drivers BIGINT,
  total_tasks BIGINT,
  assigned_tasks BIGINT,
  in_progress_tasks BIGINT,
  completed_tasks BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT
    (SELECT COUNT(*) FROM public.cases) as total_cases,
    (SELECT COUNT(*) FROM public.cases WHERE status = 'OPEN') as open_cases,
    (SELECT COUNT(*) FROM public.cases WHERE status = 'CLOSED') as closed_cases,
    (SELECT COUNT(*) FROM public.cases WHERE status = 'CANCELLED') as cancelled_cases,
    (SELECT COUNT(*) FROM public.users WHERE role = 'DRIVER') as total_drivers,
    (SELECT COUNT(*) FROM public.users WHERE role = 'DRIVER' AND status = 'ACTIVE') as active_drivers,
    (SELECT COUNT(*) FROM public.tasks) as total_tasks,
    (SELECT COUNT(*) FROM public.tasks WHERE status = 'ASSIGNED') as assigned_tasks,
    (SELECT COUNT(*) FROM public.tasks WHERE status = 'IN_PROGRESS') as in_progress_tasks,
    (SELECT COUNT(*) FROM public.tasks WHERE status = 'COMPLETED') as completed_tasks;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================
-- 7. DRIVER LOCATION CLEANUP
-- ================================

-- Function to clean up old driver locations (for performance)
CREATE OR REPLACE FUNCTION public.cleanup_old_driver_locations(
  p_days_to_keep INTEGER DEFAULT 30
) RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM public.driver_locations
  WHERE recorded_at < NOW() - INTERVAL '1 day' * p_days_to_keep;
  
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ================================
-- 8. TASK COMPLETION HELPER
-- ================================

-- Function to complete a task
CREATE OR REPLACE FUNCTION public.complete_task(
  p_task_id UUID,
  p_driver_id UUID,
  p_lat NUMERIC DEFAULT NULL,
  p_lng NUMERIC DEFAULT NULL
) RETURNS BOOLEAN AS $$
DECLARE
  task_record RECORD;
  total_distance NUMERIC;
  total_duration INTEGER;
BEGIN
  -- Get task details
  SELECT * INTO task_record
  FROM public.tasks
  WHERE id = p_task_id AND assignee_id = p_driver_id AND status = 'IN_PROGRESS';
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found or not in progress for this driver';
  END IF;
  
  -- Calculate real distance and duration from driver_locations
  SELECT 
    COALESCE(SUM(distance_to_dest_km), 0),
    EXTRACT(EPOCH FROM (NOW() - task_record.started_at)) / 60
  INTO total_distance, total_duration
  FROM public.driver_locations
  WHERE task_id = p_task_id;
  
  -- Update task as completed
  UPDATE public.tasks SET
    status = 'COMPLETED',
    completed_at = NOW(),
    real_distance_km = total_distance,
    real_duration_min = total_duration
  WHERE id = p_task_id;
  
  -- Log arrival event
  INSERT INTO public.task_events (
    task_id,
    actor_id,
    event_type,
    old_status,
    new_status,
    lat,
    lng,
    recorded_at
  ) VALUES (
    p_task_id,
    p_driver_id,
    'ARRIVAL',
    'IN_PROGRESS',
    'COMPLETED',
    p_lat,
    p_lng,
    NOW()
  );
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
