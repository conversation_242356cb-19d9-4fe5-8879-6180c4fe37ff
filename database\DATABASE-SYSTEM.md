1. ENUM Tip <PERSON>, projede kull<PERSON> tüm ENUM tipleri aşağıda oluşturulmuştur.

sql
Kopyala
Düzenle
-- 1.1. <PERSON><PERSON><PERSON><PERSON><PERSON> Rolleri ve Durumları
CREATE TYPE user_role AS ENUM (
  'ADMIN',
  'DRIVER',
  'FAMILY'
);

CREATE TYPE user_status AS ENUM (
  'ACTIVE',
  'ON_LEAVE',
  'INACTIVE'
);

-- 1.2. <PERSON><PERSON><PERSON> Du<PERSON> ve Defin Türü
CREATE TYPE case_status AS ENUM (
  'OPEN',
  'CLOSED',
  'CANCELLED'
);

CREATE TYPE burial_type AS ENUM (
  'DE',    -- Almanya (Deutschland)
  'TR'     -- Türki<PERSON>
);

-- 1.3. Belge Türleri
CREATE TYPE document_type AS ENUM (
  'DEATH_CERT',     -- <PERSON><PERSON><PERSON><PERSON> belgesi
  'FORMUL_C',       -- <PERSON><PERSON><PERSON><PERSON> <PERSON> belgesi
  'CARGO_WAYBILL',  -- <PERSON><PERSON>
  'PHOTO'           -- Fotoğraf vb.
);

-- 1.4. <PERSON><PERSON><PERSON>im Kanalı ve Durumu
CREATE TYPE notification_channel AS ENUM (
  'PUSH',
  'SMS',
  'EMAIL'
);

CREATE TYPE notification_status AS ENUM (
  'SENT',
  'FAIL'
);

-- 1.5. Yer (Place) Tipleri
CREATE TYPE place_type AS ENUM (
  'MORGUE',
  'HOME',
  'AIRPORT',
  'CONSULATE',
  'CEMETERY',
  'OTHER'
);

-- 1.6. Görev (Task) Tipi ve Durumu
CREATE TYPE task_type AS ENUM (
  'PICK_UP_FROM_MORGUE',  -- Morg’dan alma
  'PICK_UP_FROM_HOME',    -- İkamet adresinden alma
  'TO_AIRPORT',           -- Havaalanına götürme
  'TO_CONSULATE',         -- Konsolosluğa götürme
  'DELIVERED'             -- Hedefe teslim edildi
);

CREATE TYPE task_status AS ENUM (
  'ASSIGNED',     -- Atandı, sürücü henüz başlatmadı
  'IN_PROGRESS',  -- Sürücü hareket etti, görev devam ediyor
  'COMPLETED',    -- Görev tamamlandı
  'FAILED'        -- Başarısız oldu (iptal, geri dönme, vs.)
);

-- 1.7. Görev Olay (Event) Tipleri
CREATE TYPE task_event_type AS ENUM (
  'STATUS_CHANGE',    -- Statü değişimi (ASSIGNED→IN_PROGRESS, vb.)
  'HEARTBEAT',        -- Periyodik konum güncellemesi
  'ARRIVAL',          -- Hedefe varma olayı
  'MANUAL_OVERRIDE'   -- Admin/sürücü manuel müdahale (iptal, yeniden atama, vs.)
);
2. Temel Tablolar
Aşağıda, eski şemada (DATABASE-SYSTEM) bulunan “users”, “deceased”, “cases”, “documents”, “notifications” ve “lookup_values” tabloları, gerekirse açıklamalarıyla birlikte yeniden düzenlenmiş hâlde yer almaktadır.

Not: Aşağıdaki kısımların büyük çoğunluğu, orijinal dosyadaki yapıyı (users, deceased, cases, vb.) muhafaza etmekte olup, yalnızca eksik sütun açıklamaları ve ilişkiler netleştirilmiştir.

2.1. users
sql
Kopyala
Düzenle
CREATE TABLE users (
  id          UUID              PRIMARY KEY,
  role        user_role         NOT NULL,                     -- ADMIN / DRIVER / FAMILY
  full_name   TEXT              NOT NULL,
  email       TEXT              NOT NULL UNIQUE,
  phone       TEXT                        NULL,
  status      user_status        NOT NULL DEFAULT 'ACTIVE',   -- ACTIVE / ON_LEAVE / INACTIVE
  created_at  TIMESTAMPTZ        NOT NULL DEFAULT now()
);

-- INDEX ÖNERİSİ: Aktif sürücü listesi için
CREATE INDEX idx_users_role_status ON users (role, status);
açıklama:

role='ADMIN' → yönetici, görev atama gibi işlemler yapar.

role='DRIVER' → görev üstlenen sürücü.

role='FAMILY' → cenaze vakasıyla ilişkili aile üyesi.

2.2. deceased
sql
Kopyala
Düzenle
CREATE TABLE deceased (
  id                UUID              PRIMARY KEY,
  ditib_member_id   TEXT              UNIQUE,                     -- DITIB üyelik ID’si (salt okunur)
  full_name         TEXT              NOT NULL,
  nationality       TEXT                        NULL,
  gender            TEXT                        NULL,
  date_of_death     DATE              NOT NULL,
  place_of_death    TEXT                        NULL,
  place_of_burial   TEXT                        NULL,
  family_name       TEXT              NOT NULL,                   -- Aile soyadı
  family_email      TEXT              NOT NULL,                   -- Aile iletişim e-postası
  family_phone      TEXT                        NULL,                   -- Aile telefon numarası
  created_at        TIMESTAMPTZ        NOT NULL DEFAULT now()
);
açıklama:

DITIB’den gelen senkron amacıyla ditib_member_id tutulur. Genellikle sadece okunur.

place_of_death, place_of_burial alanları serbest metin; gerekirse daha sonra konum referanslı yapıya dönüştürülebilir.

2.3. cases
sql
Kopyala
Düzenle
CREATE TABLE cases (
  id             UUID              PRIMARY KEY,
  deceased_id    UUID              NOT NULL REFERENCES deceased(id) ON DELETE CASCADE,
  family_user_id UUID              NOT NULL REFERENCES users(id)    ON DELETE RESTRICT,
  status         case_status       NOT NULL DEFAULT 'OPEN',       -- OPEN / CLOSED / CANCELLED
  burial_type    burial_type       NOT NULL,                        -- DE / TR
  created_at     TIMESTAMPTZ        NOT NULL DEFAULT now()
);

-- INDEX ÖNERİSİ: Aile üyesine göre vakaları hızla çekmek için
CREATE INDEX idx_cases_family_user ON cases (family_user_id);
açıklama:

deceased_id silindiğinde ilgili vaka da otomatik silinir.

family_user_id sadece role=’FAMILY’ olan kullanıcılarla ilişkilendirilir.

burial_type='DE' → defin Almanya’da, 'TR' → defin Türkiye’de.

2.4. documents
sql
Kopyala
Düzenle
CREATE TABLE documents (
  id            UUID              PRIMARY KEY,
  case_id       UUID              NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  doc_type      document_type     NOT NULL,                          -- DEATH_CERT, FORMUL_C, vb.
  storage_path  TEXT              NOT NULL,                          -- Dosya sistemi veya S3 gibi bir yol
  uploaded_by   UUID              NOT NULL REFERENCES users(id),      -- ADMIN / DRIVER
  created_at    TIMESTAMPTZ        NOT NULL DEFAULT now()
);

-- INDEX ÖNERİSİ: Belgelere vakaya göre hızlı erişim
CREATE INDEX idx_documents_case ON documents (case_id);
2.5. notifications
sql
Kopyala
Düzenle
CREATE TABLE notifications (
  id            UUID                     PRIMARY KEY,
  case_id       UUID        NOT NULL      REFERENCES cases(id) ON DELETE CASCADE,
  channel       notification_channel NOT NULL,                -- PUSH / SMS / EMAIL
  recipient     TEXT             NOT NULL,                    -- Alıcı (telefon numarası, e-posta veya cihaz token)
  template      TEXT             NOT NULL,                    -- Şablon metin veya şablon adı
  sent_at       TIMESTAMPTZ      NOT NULL DEFAULT now(),       -- Gönderim zaman damgası
  status        notification_status NOT NULL DEFAULT 'SENT'    -- SENT / FAIL
);

-- INDEX ÖNERİSİ: Vakaya göre bildirim geçmişi
CREATE INDEX idx_notifications_case ON notifications (case_id);
Not: Orijinal yapıda sütun adı to text; burada hem anlaşılırlık hem SQL uyumluluğu için recipient olarak değiştirildi.

2.6. lookup_values
sql
Kopyala
Düzenle
CREATE TABLE lookup_values (
  id        SERIAL            PRIMARY KEY,
  category  TEXT              NOT NULL,     -- Örn: “COUNTRY_CODES”, “TASK_PRIORITIES” vb.
  code      TEXT              NOT NULL,     -- Örn: “TR”, “DE”, “LOW”, “HIGH”
  label_tr  TEXT                        NULL,  -- Türkçe açıklama
  label_de  TEXT                        NULL   -- Almanca açıklama
);

-- INDEX ÖNERİSİ: Kategori bazlı aramalar için
CREATE INDEX idx_lookup_values_category ON lookup_values (category);
açıklama: Gelecekte ortaya çıkabilecek ek kod/dil mekanizmaları için rezerve edilmiş genel bir tablo. Şu an kullanılmıyor.

3. Yeni/Oluşturulan Tablolar
Aşağıda, “Görev atama ve canlı takibi” mimarisini destekleyen yeni/düzenlenmiş tablolar yer almaktadır. Orijinal tasks ve task_status_history tabloları, sadeleştirilerek ve genişletilerek yeniden yazılmıştır.

3.1. places
Amaç: Morg, ev, havaalanı, konsolosluk, mezarlık vb. statik ve dinamik konumları tutmak.

sql
Kopyala
Düzenle
CREATE TABLE places (
  id          UUID            PRIMARY KEY,
  place_type  place_type      NOT NULL,                       -- MORGUE / HOME / AIRPORT / CONSULATE / CEMETERY / OTHER
  name        TEXT            NOT NULL,                       -- Örn: “Berlin-West Morgağı”, “Muster Ailesi Evi” vb.
  address     TEXT                        NULL,               -- Tam adres (serbest metin)
  lat         NUMERIC(10,7)   NOT NULL,                       -- WGS-84 enlem
  lng         NUMERIC(10,7)   NOT NULL,                       -- WGS-84 boylam
  country     CHAR(2)         NOT NULL, DEFAULT 'TR',         -- ISO Ülke Kodu (DE veya TR)
  created_at  TIMESTAMPTZ     NOT NULL DEFAULT now()
);

-- INDEX ÖNERİSİ: Coğrafi sorgular için PostGIS eklenecekse: 
-- ALTER TABLE places ADD COLUMN geom GEOMETRY(Point, 4326);
-- CREATE INDEX idx_places_geom ON places USING GIST (geom);
açıklama:

place_type ile türü (morg, ev vb.) belirlenir.

İleride harita servisi (Google, Mapbox vb.) entegrasyonu için coğrafi indeks önerildi.

3.2. tasks
Not: Orijinal tasks tablosundaki (status=PENDING/ACTIVE/DONE/FAILED/OVERDUE) ve sub_status sütunları çıkarılmış, yerine daha net bir akış (ASSIGNED→IN_PROGRESS→COMPLETED/FAILED) benimsenmiştir. Ayrıca, origin_place_id ve dest_place_id eklendi.

sql
Kopyala
Düzenle
CREATE TABLE tasks (
  id                UUID            PRIMARY KEY,
  case_id           UUID            NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  assignee_id       UUID                        REFERENCES users(id),   -- Görevi üstlenecek sürücü (NULL = henüz atanmamış)
  task_type         task_type       NOT NULL,                       -- Görev türü
  status            task_status     NOT NULL DEFAULT 'ASSIGNED',   -- ASSIGNED / IN_PROGRESS / COMPLETED / FAILED
  origin_place_id   UUID                        REFERENCES places(id), -- Başlangıç konumu (morg, ev, vb.)
  dest_place_id     UUID                        REFERENCES places(id), -- Hedef konum (havaalanı, konsolosluk, mezarlık vb.)
  scheduled_at      TIMESTAMPTZ      NOT NULL,                       -- Admin tarafından planlanan tarih/saat
  started_at        TIMESTAMPTZ                         NULL,           -- Sürücü “Start” dediği zaman
  completed_at      TIMESTAMPTZ                         NULL,           -- Sürücü “Arrived” dediği zaman
  est_distance_km   NUMERIC(7,2)                      NULL,           -- Tahmini mesafe (kilometre)
  est_duration_min  INTEGER                           NULL,           -- Tahmini süre (dakika)
  real_distance_km  NUMERIC(7,2)                      NULL,           -- Gerçekleşen mesafe (kilometre)
  real_duration_min INTEGER                           NULL,           -- Gerçekleşen süre (dakika)
  notes             TEXT                              NULL,           -- Açıklama / ekstra bilgi
  created_at        TIMESTAMPTZ      NOT NULL DEFAULT now()
);

-- INDEX ÖNERİSİ: 
CREATE INDEX idx_tasks_assignee_status ON tasks (assignee_id, status);
CREATE INDEX idx_tasks_case ON tasks (case_id);
açıklama:

case_id: Hangi cenaze vakasına ait olduğu.

assignee_id: Sürücü kullanıcı ID’si; atanmamışsa NULL kalır.

task_type: Morgdan alma, konsolosluğa götürme gibi iş akışı adım türleri.

status:

ASSIGNED: Admin atadı, sürücü henüz “Start” butonuna basmadı.

IN_PROGRESS: İlk konum alındıktan sonra görev devam ediyor.

COMPLETED: Sürücü hedefe vardığında görev sonlandırıldı.

FAILED: Görev iptal edildi veya beklenmedik hata oluştu.

origin_place_id / dest_place_id: Her görev için başlangıç ve hedef konum referansları.

scheduled_at: Admin’in sürücüye planladığı tarih/saat.

started_at ve completed_at: Workflow süreç takibi için.

est_* / real_*: Mesafe ve süre tahmini / gerçekleşeni; canlı takip mantığında güncellenebilir.

3.3. driver_locations
Amaç: Görevin her noktadaki canlı konum ping’lerini kaydetmek.

sql
Kopyala
Düzenle
CREATE TABLE driver_locations (
  id                  BIGSERIAL       PRIMARY KEY,
  driver_id           UUID             NOT NULL REFERENCES users(id),  -- Sürücü ID’si (role=DRIVER)
  task_id             UUID             NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,  
  lat                 NUMERIC(10,7)    NOT NULL,     -- Enlem
  lng                 NUMERIC(10,7)    NOT NULL,     -- Boylam
  recorded_at         TIMESTAMPTZ       NOT NULL DEFAULT now(),  
  distance_to_dest_km NUMERIC(7,2)                      NULL,   -- Hesaplanan o anki mesafe (km)
  eta_min             INTEGER                           NULL    -- Hesaplanan o anki tahmini süre (dakika)
);

-- INDEX ÖNERİSİ:
CREATE INDEX idx_driver_locations_driver_time 
    ON driver_locations (driver_id, recorded_at DESC);

CREATE INDEX idx_driver_locations_task_time 
    ON driver_locations (task_id, recorded_at DESC);
açıklama:

Mobil uygulama, örneğin her 15 saniyede bir driver_locations tablosuna yeni satır ekler (lat, lng, timestamp).

Her eklemede dışarıda veya trigger/worker içinde Haversine formülü veya harita servisi API’si kullanılarak distance_to_dest_km ve eta_min hesaplanıp bu sütunlara yazılabilir.

Partitioning (Parçalama): Bu tablo hızla büyüyeceği için ay bazlı veya haftalık partisyon önerilir.

3.4. task_events
Amaç: Tüm görev süreç değişikliklerini (statü, varış, heartbeat/konum güncelleme vb.) audit log olarak kayıt altına almak.

sql
Kopyala
Düzenle
CREATE TABLE task_events (
  id            BIGSERIAL         PRIMARY KEY,
  task_id       UUID              NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  actor_id      UUID              NOT NULL REFERENCES users(id),  -- Olayı tetikleyen (ADMIN/DRIVER)
  event_type    task_event_type   NOT NULL,                       -- STATUS_CHANGE / HEARTBEAT / ARRIVAL / MANUAL_OVERRIDE
  old_status    task_status                       NULL,           -- Eğer statü değiştiyse eski statü
  new_status    task_status                       NULL,           -- Değişiklik sonucu yeni statü
  lat           NUMERIC(10,7)                    NULL,           -- Olay anındaki enlem (varışta veya heartbeat’te kullanılır)
  lng           NUMERIC(10,7)                    NULL,           -- Olay anındaki boylam
  recorded_at   TIMESTAMPTZ        NOT NULL DEFAULT now(),       -- Olay zaman damgası
  extra         JSONB                            NULL            -- Gerekirse hata mesajı, açıklama, vs.
);

-- INDEX ÖNERİSİ: 
CREATE INDEX idx_task_events_task_time ON task_events (task_id, recorded_at DESC);
açıklama:

STATUS_CHANGE: old_status → new_status yazılır (örn. ASSIGNED→IN_PROGRESS).

HEARTBEAT: Mobil uygulamadan her konum ping’inde (statü değişmiyorsa old_status/new_status NULL kalabilir, ancak event_type='HEARTBEAT' olarak tutulur).

ARRIVAL: Sürücü hedefe varınca tetiklenir.

MANUAL_OVERRIDE: Admin veya sürücünün görev iptal, yeniden atama, vb. gibi özel durumları.

extra: JSON formatında ek bilgi (hata kodu, mesaj, not vb.) saklamak için.

4. Mevcut İlişkiler ve Örnek DDL Komutları
Aşağıda, tüm ENUM’lar ve tablolar bir arada, sırasıyla oluşturulacak şekilde tek bir SQL betiği (script) formatında verilmiştir. Böylece “ilk çalıştırma”da ihtiyaç duyulan bütün yapılar otomatik olarak kurulur.

sql
Kopyala
Düzenle
-- ================================
-- 1. ENUM TIPLERİ
-- ================================
CREATE TYPE user_role AS ENUM (
  'ADMIN',
  'DRIVER',
  'FAMILY'
);

CREATE TYPE user_status AS ENUM (
  'ACTIVE',
  'ON_LEAVE',
  'INACTIVE'
);

CREATE TYPE case_status AS ENUM (
  'OPEN',
  'CLOSED',
  'CANCELLED'
);

CREATE TYPE burial_type AS ENUM (
  'DE',
  'TR'
);

CREATE TYPE document_type AS ENUM (
  'DEATH_CERT',
  'FORMUL_C',
  'CARGO_WAYBILL',
  'PHOTO'
);

CREATE TYPE notification_channel AS ENUM (
  'PUSH',
  'SMS',
  'EMAIL'
);

CREATE TYPE notification_status AS ENUM (
  'SENT',
  'FAIL'
);

CREATE TYPE place_type AS ENUM (
  'MORGUE',
  'HOME',
  'AIRPORT',
  'CONSULATE',
  'CEMETERY',
  'OTHER'
);

CREATE TYPE task_type AS ENUM (
  'PICK_UP_FROM_MORGUE',
  'PICK_UP_FROM_HOME',
  'TO_AIRPORT',
  'TO_CONSULATE',
  'DELIVERED'
);

CREATE TYPE task_status AS ENUM (
  'ASSIGNED',
  'IN_PROGRESS',
  'COMPLETED',
  'FAILED'
);

CREATE TYPE task_event_type AS ENUM (
  'STATUS_CHANGE',
  'HEARTBEAT',
  'ARRIVAL',
  'MANUAL_OVERRIDE'
);


-- =============================
-- 2. TEMEL TABLOLAR
-- =============================

-- 2.1. users
CREATE TABLE users (
  id          UUID              PRIMARY KEY,
  role        user_role         NOT NULL,
  full_name   TEXT              NOT NULL,
  email       TEXT              NOT NULL UNIQUE,
  phone       TEXT                        NULL,
  status      user_status        NOT NULL DEFAULT 'ACTIVE',
  created_at  TIMESTAMPTZ        NOT NULL DEFAULT now()
);

CREATE INDEX idx_users_role_status ON users (role, status);

-- 2.2. deceased
CREATE TABLE deceased (
  id                UUID              PRIMARY KEY,
  ditib_member_id   TEXT              UNIQUE,
  full_name         TEXT              NOT NULL,
  nationality       TEXT                        NULL,
  gender            TEXT                        NULL,
  date_of_death     DATE              NOT NULL,
  place_of_death    TEXT                        NULL,
  place_of_burial   TEXT                        NULL,
  family_name       TEXT              NOT NULL,
  family_email      TEXT              NOT NULL,
  family_phone      TEXT                        NULL,
  created_at        TIMESTAMPTZ        NOT NULL DEFAULT now()
);

-- 2.3. cases
CREATE TABLE cases (
  id             UUID              PRIMARY KEY,
  deceased_id    UUID              NOT NULL REFERENCES deceased(id) ON DELETE CASCADE,
  family_user_id UUID              NOT NULL REFERENCES users(id)     ON DELETE RESTRICT,
  status         case_status       NOT NULL DEFAULT 'OPEN',
  burial_type    burial_type       NOT NULL,
  created_at     TIMESTAMPTZ        NOT NULL DEFAULT now()
);

CREATE INDEX idx_cases_family_user ON cases (family_user_id);

-- 2.4. documents
CREATE TABLE documents (
  id            UUID              PRIMARY KEY,
  case_id       UUID              NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  doc_type      document_type     NOT NULL,
  storage_path  TEXT              NOT NULL,
  uploaded_by   UUID              NOT NULL REFERENCES users(id),
  created_at    TIMESTAMPTZ        NOT NULL DEFAULT now()
);

CREATE INDEX idx_documents_case ON documents (case_id);

-- 2.5. notifications
CREATE TABLE notifications (
  id            UUID                     PRIMARY KEY,
  case_id       UUID        NOT NULL      REFERENCES cases(id) ON DELETE CASCADE,
  channel       notification_channel NOT NULL,
  recipient     TEXT             NOT NULL,
  template      TEXT             NOT NULL,
  sent_at       TIMESTAMPTZ      NOT NULL DEFAULT now(),
  status        notification_status NOT NULL DEFAULT 'SENT'
);

CREATE INDEX idx_notifications_case ON notifications (case_id);

-- 2.6. lookup_values
CREATE TABLE lookup_values (
  id        SERIAL            PRIMARY KEY,
  category  TEXT              NOT NULL,
  code      TEXT              NOT NULL,
  label_tr  TEXT                        NULL,
  label_de  TEXT                        NULL
);

CREATE INDEX idx_lookup_values_category ON lookup_values (category);


-- =============================
-- 3. YENİ / GÜNCELLENMİŞ TABLOLAR
-- =============================

-- 3.1. places
CREATE TABLE places (
  id          UUID            PRIMARY KEY,
  place_type  place_type      NOT NULL,
  name        TEXT            NOT NULL,
  address     TEXT                        NULL,
  lat         NUMERIC(10,7)   NOT NULL,
  lng         NUMERIC(10,7)   NOT NULL,
  country     CHAR(2)         NOT NULL DEFAULT 'TR',
  created_at  TIMESTAMPTZ     NOT NULL DEFAULT now()
);

-- (Opsiyonel: PostGIS kullanılacaksa geom kolonu eklenebilir)
-- ALTER TABLE places ADD COLUMN geom GEOMETRY(Point, 4326);
-- CREATE INDEX idx_places_geom ON places USING GIST (geom);

-- 3.2. tasks
CREATE TABLE tasks (
  id                UUID            PRIMARY KEY,
  case_id           UUID            NOT NULL REFERENCES cases(id) ON DELETE CASCADE,
  assignee_id       UUID                        REFERENCES users(id),
  task_type         task_type       NOT NULL,
  status            task_status     NOT NULL DEFAULT 'ASSIGNED',
  origin_place_id   UUID                        REFERENCES places(id),
  dest_place_id     UUID                        REFERENCES places(id),
  scheduled_at      TIMESTAMPTZ      NOT NULL,
  started_at        TIMESTAMPTZ                         NULL,
  completed_at      TIMESTAMPTZ                         NULL,
  est_distance_km   NUMERIC(7,2)                      NULL,
  est_duration_min  INTEGER                           NULL,
  real_distance_km  NUMERIC(7,2)                      NULL,
  real_duration_min INTEGER                           NULL,
  notes             TEXT                              NULL,
  created_at        TIMESTAMPTZ      NOT NULL DEFAULT now()
);

CREATE INDEX idx_tasks_assignee_status ON tasks (assignee_id, status);
CREATE INDEX idx_tasks_case ON tasks (case_id);

-- 3.3. driver_locations
CREATE TABLE driver_locations (
  id                  BIGSERIAL       PRIMARY KEY,
  driver_id           UUID             NOT NULL REFERENCES users(id),
  task_id             UUID             NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  lat                 NUMERIC(10,7)    NOT NULL,
  lng                 NUMERIC(10,7)    NOT NULL,
  recorded_at         TIMESTAMPTZ       NOT NULL DEFAULT now(),
  distance_to_dest_km NUMERIC(7,2)                      NULL,
  eta_min             INTEGER                           NULL
);

CREATE INDEX idx_driver_locations_driver_time 
    ON driver_locations (driver_id, recorded_at DESC);
CREATE INDEX idx_driver_locations_task_time 
    ON driver_locations (task_id, recorded_at DESC);

-- 3.4. task_events
CREATE TABLE task_events (
  id            BIGSERIAL         PRIMARY KEY,
  task_id       UUID              NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  actor_id      UUID              NOT NULL REFERENCES users(id),
  event_type    task_event_type   NOT NULL,
  old_status    task_status                       NULL,
  new_status    task_status                       NULL,
  lat           NUMERIC(10,7)                    NULL,
  lng           NUMERIC(10,7)                    NULL,
  recorded_at   TIMESTAMPTZ        NOT NULL DEFAULT now(),
  extra         JSONB                            NULL
);

CREATE INDEX idx_task_events_task_time ON task_events (task_id, recorded_at DESC);
5. Tablo ve Sütun Açıklamaları (Detaylı Anlatım)
Aşağıda, her tablonun önemli sütunları ve iş mantığı açısından hangi amaçla kullanıldığı özetlenmiştir.

5.1. users
id (UUID): Her kullanıcının (admin, sürücü, aile) tekil kimliği.

role: Kullanıcının sistemi hangi amaçla kullandığı (yetkilendirme açısından kritik).

full_name / email / phone: İletişim bilgileri.

status: Sürücülerin durumu (aktif, izinli, pasif) veya admin/aile kullanıcılarının durumu.

created_at: Kayıt zamanı.

5.2. deceased
ditib_member_id: DITIB’den gelen üye kimliği; yalnızca okunur, değiştirilemez.

full_name, nationality, gender, date_of_death, place_of_death, place_of_burial: Merhum hakkındaki temel demografik/veri bilgileri.

family_name, family_email, family_phone: Cenazenin aile iletişim bilgileri.

created_at: Veri kaydının oluşturulma zamanı.

5.3. cases
deceased_id: Hangi merhuma ait vaka olduğu.

family_user_id: Aile üyesine atanmış kullanıcı ID’si (role='FAMILY').

status: OPEN → henüz süreç devam ediyor; CLOSED → defin tamamlandı; CANCELLED → vaka iptal edildi.

burial_type: DE veya TR → defin işleminin yapılacağı ülke.

created_at: Vaka oluşturulma zamanı.

5.4. documents
case_id: Hangi vakaya ait belge olduğu.

doc_type: Belge türü (ölüm belgesi, form, kargo irsaliyesi vb.).

storage_path: Dosya yolu veya objenin bulut konumu.

uploaded_by: Dokümanı yükleyen kullanıcı (admin veya sürücü).

created_at: Belgenin yüklenme zamanı.

5.5. notifications
case_id: Hangi vakayla ilgili olduğu.

channel: PUSH / SMS / EMAIL.

recipient: Hangi telefon/e-posta/cihaz token adresine gönderildiği.

template: Kullanılan şablon metni veya şablon adı.

sent_at: Gönderim zaman damgası.

status: SENT (başarılı) veya FAIL (başarısız).

5.6. lookup_values
category, code, label_tr, label_de: Kod sözlüğü ihtiyacı için serbest form. Örneğin ileride ek dil destekleri veya kod/yapı genişletmelerinde kullanılır.

5.7. places
place_type: Statik/dinamik konum türünü belirler (MORGUE, HOME, AIRPORT, CONSULATE, CEMETERY veya OTHER).

name / address / lat / lng: Konumun adı, adresi ve coğrafi koordinatları.

country: İki haneli ISO kod (DE, TR).

created_at: Kayıt zamanı.

Kullanım Örneği:

Morg kaydı için: (id='...', place_type='MORGUE', name='Berlin-Zafer Morgağı', address='***', lat=52.520008, lng=13.404954, country='DE')

Ev kaydı için: (id='...', place_type='HOME', name='Yılmaz Ailesi Evi', address='***', lat=41.008237, lng=28.978358, country='TR')

5.8. tasks
case_id: Hangi cenaze vakasına ait olduğu.

assignee_id: Atanan sürücü (kullanıcı ID’si).

task_type: Görevin maksadı (morgdan alma, konsolosluğa sevk vb.).

status: Görev akış durumu; başlangıçta ASSIGNED, sürücü “Start” dediğinde IN_PROGRESS, hedefe varınca COMPLETED, hata/iptalde FAILED.

origin_place_id: Görev başlangıç adresi (place tablosuna referans).

dest_place_id: Görev sonu adresi.

scheduled_at: Admin’in sürücüye bildirdiği planlanan tarih/saat.

started_at: Sürücü “Start” dediği zaman otomatik atanacak. Aynı istekte ilk konum verisi de driver_locations tablosuna eklenecek.

completed_at: Sürücü “Arrived” dediği zaman atanacak; o ana kadar toplanan driver_locations verisi üzerinden real_distance_km / real_duration_min hesaplanabilir.

est_distance_km / est_duration_min: Bir önceki driver_locations veya task atandığı anda API/trigger tarafından hesaplanarak ön yükleme yapılabilir.

real_distance_km / real_duration_min: Görev tamamlandığında (completed_at) GPS rotası veya harita servisi API’si kullanılarak hesaplanır.

notes: Opsiyonel açıklama alanı.

created_at: Görevin ilk oluşturulma zamanı (otomatik).

5.9. driver_locations
driver_id: Her ping’de sürücü ID’si.

task_id: Hangi göreve ait bu konum bilgisinin alındığı.

lat / lng: Sürücünün o anki konumu.

recorded_at: Konum güncelleme zamanı.

distance_to_dest_km: O anki koordinattan tasks.dest_place_id’ye olan mesafe (kilometre).

eta_min: O anki koordinattan varış noktasına kalan tahmini süre (dakika).

Akış Örneği:

Admin /tasks/{id}/assign ile ilgili sürücüye task_type='PICK_UP_FROM_MORGUE' görevini atar ve scheduled_at=2025-06-05T10:00:00Z olarak ayarlar. status='ASSIGNED'.

2025-06-05T09:50:00Z’de sürücü mobil uygulamaya giriş yapar. Bir ‘Start’ butonu çıkar.

Sürücü “Start” dediğinde API, tasks.started_at=now(), tasks.status='IN_PROGRESS' yapar ve aynı istekte (örneğin HTTP body içinde) ilk konum (lat,lng) bilgisini alıp driver_locations tablosuna yazar.

Ardından ayrı bir trigger veya arka plan işçisi, bu yeni satır üzerinden distance_to_dest_km ve eta_min değerlerini harita servisi (Google, Mapbox vb.) veya Haversine formülü ile hesaplar ve ilgili driver_locations satırını günceller.

Sürücü her 15–30 saniyede bir otomatik olarak driver_locations a ping gönderir; backend aynı mantığı tekrarlar.

Sürücü hedefe varınca “Arrived” butonuna basar. API, tasks.completed_at=now(), tasks.status='COMPLETED' ve real_distance_km/real_duration_min hesaplamalarını yapar (geçen süre ve rota üzerinden).

5.10. task_events
task_id: Hangi görevle ilgili olay olduğu.

actor_id: Olayı tetikleyen kullanıcı (ADMIN veya DRIVER).

event_type:

STATUS_CHANGE: old_status → new_status yazılır.

HEARTBEAT: Sürücü her driver_locations ping’inde statü değişmiyorsa salt “kalp atışı” olarak loglanabilir.

ARRIVAL: Sürücü hedefe varınca, old_status='IN_PROGRESS', new_status='COMPLETED' ve lat,lng o anki koordinatlar kaydedilir.

MANUAL_OVERRIDE: Admin tarafından görev iptal/yeniden atama vb. durumlar.

old_status, new_status: Sadece STATUS_CHANGE veya MANUAL_OVERRIDE için geçerlidir.

lat, lng: Olay anındaki konum.

recorded_at: Olayın gerçekleşme zamanı.

extra: JSON alanı; gerekirse hata kodu, açıklama, not, stack trace gibi bilgileri saklayabilir.

Audit Mantığı:

Her statü değişimi (örn. ASSIGNED → IN_PROGRESS → COMPLETED) bir satır olarak loglanır.

Her canlı konum güncellemesi (heartbeat) ayrı bir satır olarak tutulur; ileride harita üzerinde sürücünün rotası görselleştirilebilir.

Admin’in görev iptali veya yeniden ataması da event_type='MANUAL_OVERRIDE' olarak kaydedilir.

6. Index ve Performans Önerileri
PostGIS Entegrasyonu (Opsiyonel):

places tablosunda geom kolonu (“GEOMETRY(Point, 4326)”) eklenip GIST indeksi oluşturularak coğrafi sorgular (örneğin görev mesafe sorguları) hızlandırılabilir.

driver_locations için de geom kolonu eklenip GIST indeksi eklenebilir; bu sayede rota/alan analizleri kolaylaşır.

Partitioning (Parçalama):

driver_locations tablosu hızla büyüyeceğinden aylık veya haftalık partisyon önerilir.

Örnek:

sql
Kopyala
Düzenle
-- Parent tablo
CREATE TABLE driver_locations (
  ...
) PARTITION BY RANGE (recorded_at);
-- 2025 Haziran partisyonu
CREATE TABLE driver_locations_2025_06
  PARTITION OF driver_locations
  FOR VALUES FROM ('2025-06-01') TO ('2025-07-01');
Diğer aylara benzer partisyon kayıtları eklenebilir.

Ek Index Önerileri:

tasks (assignee_id, status) → sürücünün bekleyen veya devam eden görevlerini hızlı getirir.

driver_locations (driver_id, recorded_at DESC) → sürücünün en son konumunu düşük gecikmeyle almak için.

task_events (task_id, recorded_at DESC) → bir görevin tüm olaylarını kronolojik olarak hızlıca çekmek için.

places (country) → ülkeye göre konum filtrelemeleri için (eğer sıklıkla sorgulanıyorsa).

7. Proje Örüntüsü (Workflow)
Bu şema uygulamaya göre şöyle çalışır:

Vaka Açılması

Yeni bir cenaze vakası oluşturulduğunda (cases tablosuna kayıt), kenarda bir “Edge Function” veya backend mantığı otomatik olarak ilk görev satırını ekleyebilir:

sql
Kopyala
Düzenle
INSERT INTO tasks (id, case_id, task_type, status, origin_place_id, dest_place_id, scheduled_at)
VALUES (
  gen_random_uuid(),
  :case_id,
  'PICK_UP_FROM_MORGUE',
  'ASSIGNED',
  :morgue_place_id,
  NULL,
  :planned_time
);
Bu aşamada assignee_id NULL kalır, çünkü sürücü henüz seçilmedi.

Admin Görev Ataması

Admin panelinden uygun role='DRIVER' AND status='ACTIVE' kullanıcılarından birini seçer.

Backend:

sql
Kopyala
Düzenle
UPDATE tasks
  SET assignee_id   = :driver_id,
      scheduled_at  = COALESCE(:scheduled_at, scheduled_at),
      status        = 'ASSIGNED'
WHERE id = :task_id
  AND status IN ('ASSIGNED', 'FAILED');

INSERT INTO task_events (
  task_id, actor_id, event_type, old_status, new_status
) VALUES (
  :task_id, :admin_id, 'STATUS_CHANGE', :old_status, 'ASSIGNED'
);
Bu aşamada sürücüye push / SMS ile bildirim gönderilir (notifications tablosuna kayıt eklenir).

Sürücü “Start” Dediğinde (Görev Başlangıcı)

Mobil uygulama HTTP isteği ile /tasks/{task_id}/start endpoint’ine lat/lng verilerini yollar.

API:

sql
Kopyala
Düzenle
-- 3.1. driver_locations tablosuna ilk konumu ekle
INSERT INTO driver_locations (driver_id, task_id, lat, lng, recorded_at)
VALUES (:driver_id, :task_id, :lat, :lng, now());

-- 3.2. tasks tablosunu güncelle: started_at, status
UPDATE tasks
  SET started_at = now(),
      status     = 'IN_PROGRESS',
      est_distance_km  = :initial_est_distance,
      est_duration_min = :initial_est_duration
WHERE id = :task_id;

-- 3.3. task_events tablosuna statü değişimini ekle
INSERT INTO task_events (
  task_id, actor_id, event_type, old_status, new_status, lat, lng, recorded_at
) VALUES (
  :task_id, :driver_id, 'STATUS_CHANGE', 'ASSIGNED', 'IN_PROGRESS', :lat, :lng, now()
);
Aynı anda, ayrı bir arka plan işçisi veya trigger, bu driver_locations satırı için distance_to_dest_km ve eta_min hesaplamalarını yapar (örneğin Haversine veya harita servisi API’si ile).

Sürücü Hareket Halindeyken (Heartbeat)

Mobil uygulama her X saniyede bir /tasks/{task_id}/heartbeat benzeri bir endpoint’e lat/lng gönderir.

API:

sql
Kopyala
Düzenle
INSERT INTO driver_locations (driver_id, task_id, lat, lng, recorded_at)
VALUES (:driver_id, :task_id, :lat, :lng, now());

INSERT INTO task_events (task_id, actor_id, event_type, lat, lng, recorded_at)
VALUES (:task_id, :driver_id, 'HEARTBEAT', :lat, :lng, now());

-- (Opsiyonel) Koşarak aynı anda distance_to_dest_km & eta_min hesaplanıp driver_locations kaydı güncellenir.
Bu veriler üzerinden canlı harita üzerinde sürücünün konumu ve kalan süre/mesafe anlık gösterilir.

Sürücü Hedefe Vardığında (Arrival / Görev Tamamlama)

Mobil uygulama “Arrived” butonuna bastığında /tasks/{task_id}/complete gibi bir endpoint çağrılır.

API:

sql
Kopyala
Düzenle
-- 5.1. Gerçekleşen mesafe/süre hesaplamaları (geçmiş driver_locations verisine veya harita servisi rotalama API’sine dayanarak)
-- Örnek: :calc_real_distance, :calc_real_duration hesaplandıktan sonra:

UPDATE tasks
  SET completed_at     = now(),
      status           = 'COMPLETED',
      real_distance_km = :calc_real_distance,
      real_duration_min= :calc_real_duration
WHERE id = :task_id;

-- 5.2. task_events kaydı
INSERT INTO task_events (
  task_id, actor_id, event_type, old_status, new_status, lat, lng, recorded_at
) VALUES (
  :task_id, :driver_id, 'ARRIVAL', 'IN_PROGRESS', 'COMPLETED', :lat, :lng, now()
);
Ardından notifications tablosu aracılığıyla (örn. aileye “Görev tamamlandı, defin süreci başlıyor” vb.) ileti gönderilebilir.

Admin veya Sürücü Tarafından Manuel Müdahale (Cancel / Reassign)

Örneğin sürücü aracı arızalandı, Admin yeni sürücü atamak istiyor:

sql
Kopyala
Düzenle
UPDATE tasks
  SET assignee_id = :new_driver_id,
      status      = 'ASSIGNED',
      started_at  = NULL
WHERE id = :task_id;

INSERT INTO task_events (
  task_id, actor_id, event_type, old_status, new_status, extra, recorded_at
) VALUES (
  :task_id, :admin_id, 'MANUAL_OVERRIDE', 'IN_PROGRESS', 'ASSIGNED',
  '{"reason":"Araç arızası"}', now()
);
Bu sayede audit kaydı tutulmuş olur.

8. Özet Tablo Diyagramı (Ağaç Görünümü)
pgsql
Kopyala
Düzenle
users                             # ADMIN · DRIVER · FAMILY
├─ id            UUID PK
├─ role          user_role
├─ full_name     TEXT
├─ email         TEXT UNIQUE
├─ phone         TEXT NULL
├─ status        user_status
└─ created_at    timestamptz

deceased                          # DITIB senkron
├─ id                  UUID PK
├─ ditib_member_id     TEXT UNIQUE
├─ full_name           TEXT
├─ nationality         TEXT
├─ gender              TEXT
├─ date_of_death       DATE
├─ place_of_death      TEXT
├─ place_of_burial     TEXT
├─ family_name         TEXT
├─ family_email        TEXT
├─ family_phone        TEXT NULL
└─ created_at          timestamptz

cases                             # Cenaze vakası
├─ id             UUID PK
├─ deceased_id    UUID ↘ deceased.id ON DELETE CASCADE
├─ family_user_id UUID ↘ users.id (role=FAMILY)
├─ status         case_status
├─ burial_type    burial_type
└─ created_at     timestamptz

documents                         # Evrağı meta
├─ id            UUID PK
├─ case_id       UUID ↘ cases.id ON DELETE CASCADE
├─ doc_type      document_type
├─ storage_path  TEXT
├─ uploaded_by   UUID ↘ users.id
└─ created_at    timestamptz

notifications                     # Push / SMS / Email logu
├─ id           UUID PK
├─ case_id      UUID ↘ cases.id ON DELETE CASCADE
├─ channel      notification_channel
├─ recipient    TEXT
├─ template     TEXT
├─ sent_at      timestamptz
└─ status       notification_status

lookup_values                     # Kod sözlüğü (gerekirse)
├─ id        SERIAL PK
├─ category  TEXT
├─ code      TEXT
├─ label_tr  TEXT NULL
└─ label_de  TEXT NULL

places                            # Statik/Dinamik Konumlar
├─ id          UUID PK
├─ place_type  place_type
├─ name        TEXT
├─ address     TEXT NULL
├─ lat         numeric(10,7)
├─ lng         numeric(10,7)
├─ country     CHAR(2)
└─ created_at  timestamptz

tasks                             # Görevler (Admin atar)
├─ id                UUID PK
├─ case_id           UUID ↘ cases.id ON DELETE CASCADE
├─ assignee_id       UUID NULL ↘ users.id (role=DRIVER)
├─ task_type         task_type
├─ status            task_status
├─ origin_place_id   UUID ↘ places.id NULL
├─ dest_place_id     UUID ↘ places.id NULL
├─ scheduled_at      timestamptz
├─ started_at        timestamptz NULL
├─ completed_at      timestamptz NULL
├─ est_distance_km   numeric(7,2) NULL
├─ est_duration_min  integer NULL
├─ real_distance_km  numeric(7,2) NULL
├─ real_duration_min integer NULL
├─ notes             TEXT NULL
└─ created_at        timestamptz

driver_locations                  # Sürücü Canlı Konumları
├─ id                  bigserial PK
├─ driver_id           UUID ↘ users.id
├─ task_id             UUID ↘ tasks.id ON DELETE CASCADE
├─ lat                 numeric(10,7)
├─ lng                 numeric(10,7)
├─ recorded_at         timestamptz
├─ distance_to_dest_km numeric(7,2) NULL
└─ eta_min             integer NULL

task_events                       # Görev Audit/Log Olayları
├─ id            bigserial PK
├─ task_id       UUID ↘ tasks.id ON DELETE CASCADE
├─ actor_id      UUID ↘ users.id
├─ event_type    task_event_type
├─ old_status    task_status NULL
├─ new_status    task_status NULL
├─ lat           numeric(10,7) NULL
├─ lng           numeric(10,7) NULL
├─ recorded_at   timestamptz
└─ extra         jsonb NULL
9. Sonuç ve Dikkat Edilecek Hususlar
Gereksiz Kolonlar Kaldırıldı:

Orijinal tasks.sub_status, tasks.status='OVERDUE', tasks.status='DONE' vb. yerine task_status ENUM’unda sade akış (ASSIGNED→IN_PROGRESS→COMPLETED/FAILED) kullanıldı.

İlk konum (start_lat/start_lng) gibi tekrar eden veriler yerine driver_locations tablosu tek kaynak hâline getirildi.

Canlı Konum Takibi ve Mesafe/ETA:

Uygulama ilk “Start” hamlesinde tek seferlik konum ping’i gönderir ve bu bir driver_locations kaydı olarak saklanır.

Ardından periyodik ping’ler de aynı tabloya eklenir; her seferinde ayrı bir task_events satırı da (event_type='HEARTBEAT') oluşturulabilir.

Mesafe/ETA hesaplama mantığı, tek bir merkezî noktadan (örneğin bir Postgres trigger + PL/pgSQL, veya bağımsız bir worker) gerçekleştirilip hem driver_locations, hem de tasks.est_* sütunlarına yazılabilir.

Audit / History (Denetim) Mantığı:

Tüm statü değişimleri ve kritik olaylar task_events tablosunda tutulur. Bu sayede hangi kullanıcının (admin/sürücü) hangi saatte ne işlem yaptığı tam olarak izlenir.

Almanya / Türkiye Defin Senaryoları:

cases.burial_type='DE' veya 'TR' değerine göre, görev atama akışında farklı places (mezarlık, konsolosluk, nakil yöntemleri) seçilebilir.

Örneğin Almanya’da defin için önce morgdan cenazeyi alıp konsolosluğa, oradan Türkiye’ye kargolama/kargo irsaliyesi, sonra Türkiye’deki nakil veya defin adımları ayrı ayrı tasks satırları olarak tanımlanabilir.

Performans ve Ölçeklenebilirlik:

driver_locations için zaman bazlı partisyonlama (örneğin aylık) önerilir.

Coğrafi sorgular gerekiyorsa places ve/veya driver_locations tablosuna PostGIS eklentisiyle coğrafi kolon (geom) eklenip GIST indeksi oluşturulabilir.

Sık kullanılan sütun kombinasyonlarına (örn. tasks(assignee_id,status), driver_locations(driver_id,recorded_at)) indeks önerildi.

Güvenlik / Yetkilendirme:

users.role='ADMIN' ile “Assign Driver” gibi kritik işlemler sınırlandırılmalı.

users.role='DRIVER' yalnızca kendi tasks ve driver_locations kayıtlarına erişebilmeli.

users.role='FAMILY' yalnızca kendi cases ve o vakaya ait documents, notifications verilerini okuyabilmeli.

Kaynaklar:

Eski veritabanı şema tanımları (users, deceased, cases, documents, notifications, lookup_values): DATABASE-SYSTEM

Eski tasks ve task_status_history tabloları: DATABASE-SYSTEM

Yukarıdaki tam şema, gereksiz sütunların çıkartıldığı, yeni görev-akış ve canlı takip ihtiyaçlarının eklendiği, Almanya/Türkiye defin gereksinimlerinin göz önünde bulundurulduğu güncel hâlidir. Eğer ilave bir sütun veya ilişki gerekiyorsa, ENUM’a yeni değer eklemek veya ilgili tablonun altına yeni bir FOREIGN KEY eklemek yeterli olacaktır.