-- ================================
-- DEBUG USERS TABLE
-- ================================
-- Bu dosyayı Supabase SQL Editor'de çalıştırarak
-- users tablosundaki mevcut durumu kontrol edin

-- 1. Users tablosunun var olup olmadığını kontrol et
SELECT EXISTS (
   SELECT FROM information_schema.tables 
   WHERE table_schema = 'public' 
   AND table_name = 'users'
) AS users_table_exists;

-- 2. <PERSON><PERSON><PERSON> tablo varsa, tüm kullanıcıları listele
SELECT 
    id,
    role,
    full_name,
    email,
    phone,
    status,
    created_at
FROM users 
ORDER BY created_at DESC;

-- 3. <PERSON><PERSON><PERSON><PERSON> admin kullanıcılarını kontrol et
SELECT 
    id,
    role,
    full_name,
    email,
    status
FROM users 
WHERE email IN ('<EMAIL>', '<EMAIL>')
ORDER BY email;

-- 4. <PERSON><PERSON> kullanıcı sayısını kontrol et
SELECT 
    role,
    COUNT(*) as count
FROM users 
GROUP BY role
ORDER BY role;

-- 5. Auth.users tablosundaki kullanıcıları kontrol et (sadece admin erişimi)
-- Bu sorgu sadece service_role key ile çalışır
-- SELECT 
--     id,
--     email,
--     created_at,
--     email_confirmed_at
-- FROM auth.users 
-- ORDER BY created_at DESC;

-- 6. Test için <EMAIL> kullanıcısını özel olarak ara
SELECT 
    'users table' as source,
    id,
    email,
    full_name,
    role,
    status
FROM users 
WHERE LOWER(email) = '<EMAIL>'

UNION ALL

SELECT 
    'case insensitive search' as source,
    id,
    email,
    full_name,
    role,
    status
FROM users 
WHERE email ILIKE '%<EMAIL>%';

-- 7. Email formatını kontrol et (boşluk, büyük/küçük harf vs.)
SELECT 
    email,
    LENGTH(email) as email_length,
    TRIM(email) as trimmed_email,
    LOWER(TRIM(email)) as normalized_email
FROM users 
WHERE email LIKE '%ditib.com%';
